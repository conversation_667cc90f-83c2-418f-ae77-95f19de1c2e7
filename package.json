{"name": "ChaosCruiser-game", "version": "1.0.0", "description": "A vertical scrolling shooter with reality-warping mechanics", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "node --experimental-vm-modules --no-warnings node_modules/jest/bin/jest.js", "test:watch": "node --experimental-vm-modules --no-warnings node_modules/jest/bin/jest.js --watch"}, "devDependencies": {"jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "vite": "^5.0.0"}, "dependencies": {"gg-game-sdk": "^1.3.0"}}