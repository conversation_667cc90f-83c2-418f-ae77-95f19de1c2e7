
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for vfx/WarpVFXSystem.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> / <a href="index.html">vfx</a> WarpVFXSystem.js</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/344</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/161</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/35</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/309</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a>
<a name='L456'></a><a href='#L456'>456</a>
<a name='L457'></a><a href='#L457'>457</a>
<a name='L458'></a><a href='#L458'>458</a>
<a name='L459'></a><a href='#L459'>459</a>
<a name='L460'></a><a href='#L460'>460</a>
<a name='L461'></a><a href='#L461'>461</a>
<a name='L462'></a><a href='#L462'>462</a>
<a name='L463'></a><a href='#L463'>463</a>
<a name='L464'></a><a href='#L464'>464</a>
<a name='L465'></a><a href='#L465'>465</a>
<a name='L466'></a><a href='#L466'>466</a>
<a name='L467'></a><a href='#L467'>467</a>
<a name='L468'></a><a href='#L468'>468</a>
<a name='L469'></a><a href='#L469'>469</a>
<a name='L470'></a><a href='#L470'>470</a>
<a name='L471'></a><a href='#L471'>471</a>
<a name='L472'></a><a href='#L472'>472</a>
<a name='L473'></a><a href='#L473'>473</a>
<a name='L474'></a><a href='#L474'>474</a>
<a name='L475'></a><a href='#L475'>475</a>
<a name='L476'></a><a href='#L476'>476</a>
<a name='L477'></a><a href='#L477'>477</a>
<a name='L478'></a><a href='#L478'>478</a>
<a name='L479'></a><a href='#L479'>479</a>
<a name='L480'></a><a href='#L480'>480</a>
<a name='L481'></a><a href='#L481'>481</a>
<a name='L482'></a><a href='#L482'>482</a>
<a name='L483'></a><a href='#L483'>483</a>
<a name='L484'></a><a href='#L484'>484</a>
<a name='L485'></a><a href='#L485'>485</a>
<a name='L486'></a><a href='#L486'>486</a>
<a name='L487'></a><a href='#L487'>487</a>
<a name='L488'></a><a href='#L488'>488</a>
<a name='L489'></a><a href='#L489'>489</a>
<a name='L490'></a><a href='#L490'>490</a>
<a name='L491'></a><a href='#L491'>491</a>
<a name='L492'></a><a href='#L492'>492</a>
<a name='L493'></a><a href='#L493'>493</a>
<a name='L494'></a><a href='#L494'>494</a>
<a name='L495'></a><a href='#L495'>495</a>
<a name='L496'></a><a href='#L496'>496</a>
<a name='L497'></a><a href='#L497'>497</a>
<a name='L498'></a><a href='#L498'>498</a>
<a name='L499'></a><a href='#L499'>499</a>
<a name='L500'></a><a href='#L500'>500</a>
<a name='L501'></a><a href='#L501'>501</a>
<a name='L502'></a><a href='#L502'>502</a>
<a name='L503'></a><a href='#L503'>503</a>
<a name='L504'></a><a href='#L504'>504</a>
<a name='L505'></a><a href='#L505'>505</a>
<a name='L506'></a><a href='#L506'>506</a>
<a name='L507'></a><a href='#L507'>507</a>
<a name='L508'></a><a href='#L508'>508</a>
<a name='L509'></a><a href='#L509'>509</a>
<a name='L510'></a><a href='#L510'>510</a>
<a name='L511'></a><a href='#L511'>511</a>
<a name='L512'></a><a href='#L512'>512</a>
<a name='L513'></a><a href='#L513'>513</a>
<a name='L514'></a><a href='#L514'>514</a>
<a name='L515'></a><a href='#L515'>515</a>
<a name='L516'></a><a href='#L516'>516</a>
<a name='L517'></a><a href='#L517'>517</a>
<a name='L518'></a><a href='#L518'>518</a>
<a name='L519'></a><a href='#L519'>519</a>
<a name='L520'></a><a href='#L520'>520</a>
<a name='L521'></a><a href='#L521'>521</a>
<a name='L522'></a><a href='#L522'>522</a>
<a name='L523'></a><a href='#L523'>523</a>
<a name='L524'></a><a href='#L524'>524</a>
<a name='L525'></a><a href='#L525'>525</a>
<a name='L526'></a><a href='#L526'>526</a>
<a name='L527'></a><a href='#L527'>527</a>
<a name='L528'></a><a href='#L528'>528</a>
<a name='L529'></a><a href='#L529'>529</a>
<a name='L530'></a><a href='#L530'>530</a>
<a name='L531'></a><a href='#L531'>531</a>
<a name='L532'></a><a href='#L532'>532</a>
<a name='L533'></a><a href='#L533'>533</a>
<a name='L534'></a><a href='#L534'>534</a>
<a name='L535'></a><a href='#L535'>535</a>
<a name='L536'></a><a href='#L536'>536</a>
<a name='L537'></a><a href='#L537'>537</a>
<a name='L538'></a><a href='#L538'>538</a>
<a name='L539'></a><a href='#L539'>539</a>
<a name='L540'></a><a href='#L540'>540</a>
<a name='L541'></a><a href='#L541'>541</a>
<a name='L542'></a><a href='#L542'>542</a>
<a name='L543'></a><a href='#L543'>543</a>
<a name='L544'></a><a href='#L544'>544</a>
<a name='L545'></a><a href='#L545'>545</a>
<a name='L546'></a><a href='#L546'>546</a>
<a name='L547'></a><a href='#L547'>547</a>
<a name='L548'></a><a href='#L548'>548</a>
<a name='L549'></a><a href='#L549'>549</a>
<a name='L550'></a><a href='#L550'>550</a>
<a name='L551'></a><a href='#L551'>551</a>
<a name='L552'></a><a href='#L552'>552</a>
<a name='L553'></a><a href='#L553'>553</a>
<a name='L554'></a><a href='#L554'>554</a>
<a name='L555'></a><a href='#L555'>555</a>
<a name='L556'></a><a href='#L556'>556</a>
<a name='L557'></a><a href='#L557'>557</a>
<a name='L558'></a><a href='#L558'>558</a>
<a name='L559'></a><a href='#L559'>559</a>
<a name='L560'></a><a href='#L560'>560</a>
<a name='L561'></a><a href='#L561'>561</a>
<a name='L562'></a><a href='#L562'>562</a>
<a name='L563'></a><a href='#L563'>563</a>
<a name='L564'></a><a href='#L564'>564</a>
<a name='L565'></a><a href='#L565'>565</a>
<a name='L566'></a><a href='#L566'>566</a>
<a name='L567'></a><a href='#L567'>567</a>
<a name='L568'></a><a href='#L568'>568</a>
<a name='L569'></a><a href='#L569'>569</a>
<a name='L570'></a><a href='#L570'>570</a>
<a name='L571'></a><a href='#L571'>571</a>
<a name='L572'></a><a href='#L572'>572</a>
<a name='L573'></a><a href='#L573'>573</a>
<a name='L574'></a><a href='#L574'>574</a>
<a name='L575'></a><a href='#L575'>575</a>
<a name='L576'></a><a href='#L576'>576</a>
<a name='L577'></a><a href='#L577'>577</a>
<a name='L578'></a><a href='#L578'>578</a>
<a name='L579'></a><a href='#L579'>579</a>
<a name='L580'></a><a href='#L580'>580</a>
<a name='L581'></a><a href='#L581'>581</a>
<a name='L582'></a><a href='#L582'>582</a>
<a name='L583'></a><a href='#L583'>583</a>
<a name='L584'></a><a href='#L584'>584</a>
<a name='L585'></a><a href='#L585'>585</a>
<a name='L586'></a><a href='#L586'>586</a>
<a name='L587'></a><a href='#L587'>587</a>
<a name='L588'></a><a href='#L588'>588</a>
<a name='L589'></a><a href='#L589'>589</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/**
 * WarpVFXSystem
 * Purely visual feedback system for Reality Warp events.
 * - Particle bursts for initiation/completion
 * - Screen overlays: fade, vignette, color tint, scanlines
 * - Distortion-like streaks and radial waves (drawn overlays, no game logic)
 * - UI overlay: progress/status/metrics
 * - Lightweight audio cues via Web Audio API (no external assets)
 * - Object pooling and simple LOD based on FPS
 */
&nbsp;
export class WarpVFXSystem {
<span class="fstat-no" title="function not covered" >  co</span>nstructor(canvas, gameEngine = <span class="branch-0 cbranch-no" title="branch not covered" >null)</span> {
<span class="cstat-no" title="statement not covered" >    this.canvas = canvas;</span>
<span class="cstat-no" title="statement not covered" >    this.ctx = canvas.getContext('2d');</span>
<span class="cstat-no" title="statement not covered" >    this.gameEngine = gameEngine;</span>
&nbsp;
    // State machine
<span class="cstat-no" title="statement not covered" >    this.state = 'idle'; </span>// idle | initiating | transitioning_in | active | transitioning_out | cooldown | error
&nbsp;
    // Warp session
<span class="cstat-no" title="statement not covered" >    this.currentWarp = {</span>
      type: 'basic',
      duration: 6000,
      elapsed: 0,
      cooldown: 0,
      promptSnippet: null,
    };
&nbsp;
    // Visual params (theme per warp type)
<span class="cstat-no" title="statement not covered" >    this.theme = this.getTheme('basic');</span>
&nbsp;
    // Overlays
<span class="cstat-no" title="statement not covered" >    this.overlay = { alpha: 0, targetAlpha: 0 };</span>
<span class="cstat-no" title="statement not covered" >    this.vignette = { strength: 0, target: 0 };</span>
<span class="cstat-no" title="statement not covered" >    this.tint = { r: 0.0, g: 0.0, b: 0.0, strength: 0.0 };</span>
<span class="cstat-no" title="statement not covered" >    this.noise = { time: 0 };</span>
<span class="cstat-no" title="statement not covered" >    this.screenShake = { x: 0, y: 0, t: 0, amp: 0 };</span>
&nbsp;
    // Particles (pooled)
<span class="cstat-no" title="statement not covered" >    this.particles = [];</span>
<span class="cstat-no" title="statement not covered" >    this.freeParticles = [];</span>
<span class="cstat-no" title="statement not covered" >    this.maxParticles = 400; </span>// capped; LOD will lower this
&nbsp;
    // Distortion streaks
<span class="cstat-no" title="statement not covered" >    this.streaks = [];</span>
&nbsp;
    // UI
<span class="cstat-no" title="statement not covered" >    this.showUI = true;</span>
<span class="cstat-no" title="statement not covered" >    this.debug = false;</span>
&nbsp;
    // Performance
<span class="cstat-no" title="statement not covered" >    this.lod = 1.0; </span>// 0.5..1.0
&nbsp;
    // Audio
<span class="cstat-no" title="statement not covered" >    this.audioCtx = null;</span>
<span class="cstat-no" title="statement not covered" >    this.ambientNode = null;</span>
&nbsp;
    // Timing helpers
<span class="cstat-no" title="statement not covered" >    this.transitionInMs = 900;</span>
<span class="cstat-no" title="statement not covered" >    this.transitionOutMs = 700;</span>
<span class="cstat-no" title="statement not covered" >    this.initiationMs = 500;</span>
&nbsp;
    // Stats
<span class="cstat-no" title="statement not covered" >    this.metrics = {</span>
      particles: 0,
      frameMsAvg: 0,
      frames: 0,
    };
&nbsp;
<span class="cstat-no" title="statement not covered" >    this._lastPerfSample = performance.now();</span>
  }
&nbsp;
  /**
   * Developer conveniences: attach hotkeys and expose a debug API.
   * This is optional and has no impact on game logic.
   */
<span class="fstat-no" title="function not covered" >  in</span>itDevControls() {
<span class="cstat-no" title="statement not covered" >    if (this._devControlsAttached) <span class="cstat-no" title="statement not covered" >return;</span></span>
<span class="cstat-no" title="statement not covered" >    this._devControlsAttached = true;</span>
<span class="cstat-no" title="statement not covered" >    window.addEventListener('keydown', <span class="fstat-no" title="function not covered" >(e</span>) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      if (e.code === 'KeyV') { <span class="cstat-no" title="statement not covered" >this.debug = !this.debug; </span>}</span>
<span class="cstat-no" title="statement not covered" >      if (e.code === 'KeyU') { <span class="cstat-no" title="statement not covered" >this.showUI = !this.showUI; </span>}</span>
<span class="cstat-no" title="statement not covered" >      if (e.code === 'Digit1') { <span class="cstat-no" title="statement not covered" >this.startWarp({ type: 'basic', duration: 6000 }); </span>}</span>
<span class="cstat-no" title="statement not covered" >      if (e.code === 'Digit2') { <span class="cstat-no" title="statement not covered" >this.startWarp({ type: 'advanced', duration: 9000 }); </span>}</span>
<span class="cstat-no" title="statement not covered" >      if (e.code === 'Digit3') { <span class="cstat-no" title="statement not covered" >this.startWarp({ type: 'ultimate', duration: 12000 }); </span>}</span>
<span class="cstat-no" title="statement not covered" >      if (e.code === 'KeyK') { <span class="cstat-no" title="statement not covered" >this.endWarp(true); </span>}</span>
<span class="cstat-no" title="statement not covered" >      if (e.code === 'KeyF') { <span class="cstat-no" title="statement not covered" >this.showError('Debug Warp Error'); </span>}</span>
<span class="cstat-no" title="statement not covered" >      if (e.code === 'KeyC') { <span class="cstat-no" title="statement not covered" >this.startCooldown(3000); </span>}</span>
    });
    // Minimal global hook for tweaking in console
<span class="cstat-no" title="statement not covered" >    window.WarpVFX = this;</span>
  }
&nbsp;
&nbsp;
  // Public API ---------------------------------------------------------------
&nbsp;
<span class="fstat-no" title="function not covered" >  se</span>tDebugEnabled(enabled) { <span class="cstat-no" title="statement not covered" >this.debug = !!enabled; </span>}
<span class="fstat-no" title="function not covered" >  se</span>tUIVisible(visible) { <span class="cstat-no" title="statement not covered" >this.showUI = !!visible; </span>}
&nbsp;
<span class="fstat-no" title="function not covered" >  se</span>tTheme(warpType) { <span class="cstat-no" title="statement not covered" >this.theme = this.getTheme(warpType); </span>}
&nbsp;
<span class="fstat-no" title="function not covered" >  st</span>artWarp({ type = <span class="branch-0 cbranch-no" title="branch not covered" >'basic',</span> duration = <span class="branch-0 cbranch-no" title="branch not covered" >6000,</span> effects = <span class="branch-0 cbranch-no" title="branch not covered" >[],</span> prompt = <span class="branch-0 cbranch-no" title="branch not covered" >null </span>} = <span class="branch-0 cbranch-no" title="branch not covered" >{})</span> {
<span class="cstat-no" title="statement not covered" >    this.setTheme(type);</span>
<span class="cstat-no" title="statement not covered" >    this.currentWarp.type = type;</span>
<span class="cstat-no" title="statement not covered" >    this.currentWarp.duration = duration;</span>
<span class="cstat-no" title="statement not covered" >    this.currentWarp.elapsed = 0;</span>
<span class="cstat-no" title="statement not covered" >    this.currentWarp.promptSnippet = prompt ? String(prompt).slice(0, 80) : null;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.state = 'initiating';</span>
<span class="cstat-no" title="statement not covered" >    this.overlay.targetAlpha = 0.85 * this.theme.overlayAlpha;</span>
<span class="cstat-no" title="statement not covered" >    this.vignette.target = 0.8 * this.theme.vignette;</span>
<span class="cstat-no" title="statement not covered" >    this.tint.strength = 0.0; </span>// will ramp in
<span class="cstat-no" title="statement not covered" >    this.screenShake.amp = this.theme.shake;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.spawnInitiationBurst();</span>
<span class="cstat-no" title="statement not covered" >    this.playSound('init');</span>
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  en</span>dWarp(success = <span class="branch-0 cbranch-no" title="branch not covered" >true)</span> {
<span class="cstat-no" title="statement not covered" >    if (this.state === 'idle' || this.state === 'cooldown') <span class="cstat-no" title="statement not covered" >return;</span></span>
<span class="cstat-no" title="statement not covered" >    this.state = 'transitioning_out';</span>
<span class="cstat-no" title="statement not covered" >    this.spawnCompletionBurst(success);</span>
<span class="cstat-no" title="statement not covered" >    this.playSound(success ? 'complete' : 'error');</span>
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  st</span>artCooldown(cooldownMs = <span class="branch-0 cbranch-no" title="branch not covered" >0)</span> {
<span class="cstat-no" title="statement not covered" >    this.state = 'cooldown';</span>
<span class="cstat-no" title="statement not covered" >    this.currentWarp.cooldown = cooldownMs;</span>
<span class="cstat-no" title="statement not covered" >    this.currentWarp.elapsed = 0;</span>
<span class="cstat-no" title="statement not covered" >    this.overlay.targetAlpha = 0.15 * this.theme.overlayAlpha;</span>
<span class="cstat-no" title="statement not covered" >    this.vignette.target = 0.3 * this.theme.vignette;</span>
<span class="cstat-no" title="statement not covered" >    this.stopAmbient();</span>
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  en</span>dCooldown() {
<span class="cstat-no" title="statement not covered" >    this.state = 'idle';</span>
<span class="cstat-no" title="statement not covered" >    this.overlay.targetAlpha = 0;</span>
<span class="cstat-no" title="statement not covered" >    this.vignette.target = 0;</span>
<span class="cstat-no" title="statement not covered" >    this.stopAmbient();</span>
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  sh</span>owError(message = <span class="branch-0 cbranch-no" title="branch not covered" >'Warp failed')</span> {
    // Visual red flash and shake
<span class="cstat-no" title="statement not covered" >    this.state = 'error';</span>
<span class="cstat-no" title="statement not covered" >    this.overlay.targetAlpha = 0.9;</span>
<span class="cstat-no" title="statement not covered" >    this.tint = { r: 1.0, g: 0.0, b: 0.0, strength: 0.6 };</span>
<span class="cstat-no" title="statement not covered" >    this.screenShake.amp = 12;</span>
<span class="cstat-no" title="statement not covered" >    this.spawnCompletionBurst(false);</span>
<span class="cstat-no" title="statement not covered" >    this.playSound('error');</span>
    // Auto fade back to idle UI overlay
<span class="cstat-no" title="statement not covered" >    setTimeout(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >this.endCooldown(),</span> 800);</span>
  }
&nbsp;
  // Optional glue: hook to RealityWarpManager callbacks without changing its logic
<span class="fstat-no" title="function not covered" >  re</span>gisterWithWarpManager(warpManager) {
<span class="cstat-no" title="statement not covered" >    if (!warpManager || typeof warpManager.setCallback !== 'function') <span class="cstat-no" title="statement not covered" >return;</span></span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    warpManager.setCallback('warpStart', <span class="fstat-no" title="function not covered" >(w</span>arpType, data) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      this.startWarp({ type: warpType, duration: data?.duration || 6000, effects: data?.effects || [], prompt: data?.prompt });</span>
    });
&nbsp;
<span class="cstat-no" title="statement not covered" >    warpManager.setCallback('warpEnd', <span class="fstat-no" title="function not covered" >(w</span>arpType, data) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      this.endWarp(true);</span>
    });
&nbsp;
<span class="cstat-no" title="statement not covered" >    warpManager.setCallback('cooldownStart', <span class="fstat-no" title="function not covered" >(w</span>arpType, cooldownMs) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      this.startCooldown(cooldownMs || 0);</span>
    });
&nbsp;
<span class="cstat-no" title="statement not covered" >    warpManager.setCallback('cooldownEnd', <span class="fstat-no" title="function not covered" >()</span> =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      this.endCooldown();</span>
    });
&nbsp;
<span class="cstat-no" title="statement not covered" >    warpManager.setCallback('promptGenerated', <span class="fstat-no" title="function not covered" >(w</span>arpType, prompt) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      this.currentWarp.promptSnippet = String(prompt || '').slice(0, 80);</span>
    });
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  up</span>date(dt) {
    // Performance-based LOD target from FPS
    const fps = <span class="cstat-no" title="statement not covered" >this.gameEngine ? this.gameEngine.currentFPS || 60 : 60;</span>
    const lodTarget = <span class="cstat-no" title="statement not covered" >fps &gt;= 58 ? 1.0 : fps &gt;= 45 ? 0.8 : fps &gt;= 30 ? 0.65 : 0.5;</span>
<span class="cstat-no" title="statement not covered" >    this.lod += (lodTarget - this.lod) * 0.05;</span>
&nbsp;
    // Update state machine timing
<span class="cstat-no" title="statement not covered" >    this.currentWarp.elapsed += dt;</span>
&nbsp;
    // Smooth overlays
<span class="cstat-no" title="statement not covered" >    this.overlay.alpha += (this.overlay.targetAlpha - this.overlay.alpha) * 0.12;</span>
<span class="cstat-no" title="statement not covered" >    this.vignette.strength += (this.vignette.target - this.vignette.strength) * 0.1;</span>
&nbsp;
    // Ramp tint for active
<span class="cstat-no" title="statement not covered" >    if (this.state === 'transitioning_in' || this.state === 'active') {</span>
      const target = <span class="cstat-no" title="statement not covered" >this.theme.tintStrength;</span>
<span class="cstat-no" title="statement not covered" >      this.tint.strength += (target - this.tint.strength) * 0.06;</span>
    } else {
<span class="cstat-no" title="statement not covered" >      this.tint.strength += (0 - this.tint.strength) * 0.08;</span>
    }
&nbsp;
    // Screen shake decay
<span class="cstat-no" title="statement not covered" >    this.screenShake.t += dt * 0.006;</span>
<span class="cstat-no" title="statement not covered" >    this.screenShake.amp *= 0.98;</span>
&nbsp;
    // Noise time
<span class="cstat-no" title="statement not covered" >    this.noise.time += dt * 0.0015;</span>
&nbsp;
    // State transitions
<span class="cstat-no" title="statement not covered" >    if (this.state === 'initiating' &amp;&amp; this.currentWarp.elapsed &gt;= this.initiationMs) {</span>
<span class="cstat-no" title="statement not covered" >      this.state = 'transitioning_in';</span>
<span class="cstat-no" title="statement not covered" >      this.currentWarp.elapsed = 0;</span>
<span class="cstat-no" title="statement not covered" >      this.startAmbient();</span>
    } else <span class="cstat-no" title="statement not covered" >if (this.state === 'transitioning_in' &amp;&amp; this.currentWarp.elapsed &gt;= this.transitionInMs) {</span>
<span class="cstat-no" title="statement not covered" >      this.state = 'active';</span>
<span class="cstat-no" title="statement not covered" >      this.currentWarp.elapsed = 0;</span>
    } else <span class="cstat-no" title="statement not covered" >if (this.state === 'transitioning_out' &amp;&amp; this.currentWarp.elapsed &gt;= this.transitionOutMs) {</span>
<span class="cstat-no" title="statement not covered" >      this.state = 'cooldown';</span>
<span class="cstat-no" title="statement not covered" >      this.currentWarp.elapsed = 0;</span>
<span class="cstat-no" title="statement not covered" >      this.stopAmbient();</span>
    }
&nbsp;
    // Update particles &amp; streaks
<span class="cstat-no" title="statement not covered" >    this.updateParticles(dt);</span>
<span class="cstat-no" title="statement not covered" >    this.updateStreaks(dt);</span>
&nbsp;
    // Metrics
<span class="cstat-no" title="statement not covered" >    this.metrics.particles = this.particles.length;</span>
    const now = <span class="cstat-no" title="statement not covered" >performance.now();</span>
    const dtMs = <span class="cstat-no" title="statement not covered" >now - this._lastPerfSample;</span>
<span class="cstat-no" title="statement not covered" >    this._lastPerfSample = now;</span>
<span class="cstat-no" title="statement not covered" >    this.metrics.frames += 1;</span>
<span class="cstat-no" title="statement not covered" >    this.metrics.frameMsAvg += (dtMs - this.metrics.frameMsAvg) * 0.05;</span>
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  re</span>nder(ctx) {
<span class="cstat-no" title="statement not covered" >    if (!ctx) <span class="cstat-no" title="statement not covered" >ctx = this.ctx;</span></span>
<span class="cstat-no" title="statement not covered" >    if (!ctx) <span class="cstat-no" title="statement not covered" >return;</span></span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    ctx.save();</span>
&nbsp;
    // Apply subtle screen shake to overlay layer
<span class="cstat-no" title="statement not covered" >    if (this.screenShake.amp &gt; 0.2) {</span>
      const sx = <span class="cstat-no" title="statement not covered" >Math.sin(this.screenShake.t * 6.3) * this.screenShake.amp;</span>
      const sy = <span class="cstat-no" title="statement not covered" >Math.cos(this.screenShake.t * 7.1) * this.screenShake.amp;</span>
<span class="cstat-no" title="statement not covered" >      ctx.translate(sx, sy);</span>
    }
&nbsp;
    // Transition masks (fade)
<span class="cstat-no" title="statement not covered" >    if (this.overlay.alpha &gt; 0.01) {</span>
<span class="cstat-no" title="statement not covered" >      this.drawOverlay(ctx);</span>
    }
&nbsp;
    // Distortion streaks / radial waves
<span class="cstat-no" title="statement not covered" >    this.drawStreaks(ctx);</span>
&nbsp;
    // Particles
<span class="cstat-no" title="statement not covered" >    this.drawParticles(ctx);</span>
&nbsp;
    // UI overlay (progress, status)
<span class="cstat-no" title="statement not covered" >    if (this.showUI) {</span>
<span class="cstat-no" title="statement not covered" >      this.drawUI(ctx);</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    ctx.restore();</span>
  }
&nbsp;
  // Internals ----------------------------------------------------------------
&nbsp;
<span class="fstat-no" title="function not covered" >  ge</span>tTheme(type) {
<span class="cstat-no" title="statement not covered" >    switch (type) {</span>
      case 'advanced':
<span class="cstat-no" title="statement not covered" >        return {</span>
          name: 'advanced',
          overlayAlpha: 0.7,
          vignette: 0.9,
          tint: '#1fa9ff',
          tintStrength: 0.18,
          gradient: ['rgba(0,40,120,0.7)', 'rgba(0,200,180,0.6)'],
          particleColor: '#22ddff',
          particleSecondary: '#10ffa0',
          shake: 6,
          sound: { base: 220 }
        };
      case 'ultimate':
<span class="cstat-no" title="statement not covered" >        return {</span>
          name: 'ultimate',
          overlayAlpha: 0.85,
          vignette: 1.0,
          tint: '#ff7b00',
          tintStrength: 0.28,
          gradient: ['rgba(255,120,0,0.80)', 'rgba(255,0,140,0.60)'],
          particleColor: '#ffd000',
          particleSecondary: '#ff4dd2',
          shake: 10,
          sound: { base: 160 }
        };
      case 'basic':
      default:
<span class="cstat-no" title="statement not covered" >        return {</span>
          name: 'basic',
          overlayAlpha: 0.55,
          vignette: 0.7,
          tint: '#7b61ff',
          tintStrength: 0.12,
          gradient: ['rgba(40,0,80,0.6)', 'rgba(0,120,200,0.5)'],
          particleColor: '#9da6ff',
          particleSecondary: '#7af9ff',
          shake: 4,
          sound: { base: 320 }
        };
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  sp</span>awnInitiationBurst() {
    const cx = <span class="cstat-no" title="statement not covered" >this.canvas.width / 2;</span>
    const cy = <span class="cstat-no" title="statement not covered" >this.canvas.height / 2;</span>
    const count = <span class="cstat-no" title="statement not covered" >Math.floor(80 * this.lod);</span>
<span class="cstat-no" title="statement not covered" >    for (let i = <span class="cstat-no" title="statement not covered" >0;</span> i &lt; count; i++) {</span>
      const angle = <span class="cstat-no" title="statement not covered" >(i / count) * Math.PI * 2;</span>
      const speed = <span class="cstat-no" title="statement not covered" >60 + Math.random() * 240;</span>
      const life = <span class="cstat-no" title="statement not covered" >600 + Math.random() * 500;</span>
<span class="cstat-no" title="statement not covered" >      this.emitParticle({ x: cx, y: cy }, angle, speed, life, 2 + Math.random() * 3, i % 2 === 0);</span>
    }
<span class="cstat-no" title="statement not covered" >    this.spawnStreakWave({ x: cx, y: cy }, 2);</span>
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  sp</span>awnCompletionBurst(success) {
    const cx = <span class="cstat-no" title="statement not covered" >this.canvas.width / 2;</span>
    const cy = <span class="cstat-no" title="statement not covered" >this.canvas.height / 2;</span>
    const count = <span class="cstat-no" title="statement not covered" >Math.floor((success ? 60 : 40) * this.lod);</span>
<span class="cstat-no" title="statement not covered" >    for (let i = <span class="cstat-no" title="statement not covered" >0;</span> i &lt; count; i++) {</span>
      const angle = <span class="cstat-no" title="statement not covered" >Math.random() * Math.PI * 2;</span>
      const speed = <span class="cstat-no" title="statement not covered" >40 + Math.random() * (success ? 180 : 100);</span>
      const life = <span class="cstat-no" title="statement not covered" >400 + Math.random() * 400;</span>
<span class="cstat-no" title="statement not covered" >      this.emitParticle({ x: cx, y: cy }, angle, speed, life, 2 + Math.random() * 2, i % 3 === 0);</span>
    }
<span class="cstat-no" title="statement not covered" >    this.spawnStreakWave({ x: cx, y: cy }, success ? 1 : 0.6);</span>
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  em</span>itParticle(pos, angle, speed, lifetime, size, useSecondary = <span class="branch-0 cbranch-no" title="branch not covered" >false)</span> {
    const p = <span class="cstat-no" title="statement not covered" >this.freeParticles.pop() || {};</span>
<span class="cstat-no" title="statement not covered" >    p.x = pos.x; <span class="cstat-no" title="statement not covered" ></span>p.y = pos.y;</span>
<span class="cstat-no" title="statement not covered" >    p.vx = Math.cos(angle) * speed;</span>
<span class="cstat-no" title="statement not covered" >    p.vy = Math.sin(angle) * speed;</span>
<span class="cstat-no" title="statement not covered" >    p.life = lifetime; <span class="cstat-no" title="statement not covered" ></span>p.age = 0;</span>
<span class="cstat-no" title="statement not covered" >    p.size = size; <span class="cstat-no" title="statement not covered" ></span>p.alpha = 1;</span>
<span class="cstat-no" title="statement not covered" >    p.color = useSecondary ? this.theme.particleSecondary : this.theme.particleColor;</span>
<span class="cstat-no" title="statement not covered" >    this.particles.push(p);</span>
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  up</span>dateParticles(dt) {
    const friction = <span class="cstat-no" title="statement not covered" >0.98;</span>
    const fadeRate = <span class="cstat-no" title="statement not covered" >0.0025;</span>
<span class="cstat-no" title="statement not covered" >    for (let i = <span class="cstat-no" title="statement not covered" >this.particles.length - 1;</span> i &gt;= 0; i--) {</span>
      const p = <span class="cstat-no" title="statement not covered" >this.particles[i];</span>
<span class="cstat-no" title="statement not covered" >      p.age += dt;</span>
<span class="cstat-no" title="statement not covered" >      if (p.age &gt;= p.life) {</span>
<span class="cstat-no" title="statement not covered" >        this.particles.splice(i, 1);</span>
<span class="cstat-no" title="statement not covered" >        this.freeParticles.push(p);</span>
<span class="cstat-no" title="statement not covered" >        continue;</span>
      }
<span class="cstat-no" title="statement not covered" >      p.vx *= friction; <span class="cstat-no" title="statement not covered" ></span>p.vy *= friction;</span>
<span class="cstat-no" title="statement not covered" >      p.x += p.vx * (dt / 1000);</span>
<span class="cstat-no" title="statement not covered" >      p.y += p.vy * (dt / 1000);</span>
<span class="cstat-no" title="statement not covered" >      p.alpha = Math.max(0, 1 - p.age / p.life - fadeRate * (1 - this.lod));</span>
    }
    // Cap pool to maxParticles based on LOD
    const cap = <span class="cstat-no" title="statement not covered" >Math.floor(this.maxParticles * this.lod);</span>
<span class="cstat-no" title="statement not covered" >    if (this.particles.length &gt; cap) <span class="cstat-no" title="statement not covered" >this.particles.length = cap;</span></span>
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  dr</span>awParticles(ctx) {
    const w = <span class="cstat-no" title="statement not covered" >this.canvas.width,</span> h = <span class="cstat-no" title="statement not covered" >this.canvas.height;</span>
    const margin = <span class="cstat-no" title="statement not covered" >16;</span>
<span class="cstat-no" title="statement not covered" >    for (const p of this.particles) {</span>
<span class="cstat-no" title="statement not covered" >      if (p.alpha &lt;= 0.01) <span class="cstat-no" title="statement not covered" >continue;</span></span>
<span class="cstat-no" title="statement not covered" >      if (p.x &lt; -margin || p.x &gt; w + margin || p.y &lt; -margin || p.y &gt; h + margin) <span class="cstat-no" title="statement not covered" >continue; </span></span>// cull off-screen
<span class="cstat-no" title="statement not covered" >      ctx.globalAlpha = p.alpha;</span>
<span class="cstat-no" title="statement not covered" >      ctx.fillStyle = p.color;</span>
<span class="cstat-no" title="statement not covered" >      ctx.beginPath();</span>
<span class="cstat-no" title="statement not covered" >      ctx.arc(p.x, p.y, p.size, 0, Math.PI * 2);</span>
<span class="cstat-no" title="statement not covered" >      ctx.fill();</span>
    }
<span class="cstat-no" title="statement not covered" >    ctx.globalAlpha = 1.0;</span>
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  sp</span>awnStreakWave(center, intensity = <span class="branch-0 cbranch-no" title="branch not covered" >1)</span> {
    const count = <span class="cstat-no" title="statement not covered" >Math.floor(5 * intensity * this.lod);</span>
<span class="cstat-no" title="statement not covered" >    for (let i = <span class="cstat-no" title="statement not covered" >0;</span> i &lt; count; i++) {</span>
<span class="cstat-no" title="statement not covered" >      this.streaks.push({</span>
        x: center.x,
        y: center.y,
        r: 0,
        w: 8 + Math.random() * 18,
        alpha: 0.4,
        speed: 220 + Math.random() * 220,
      });
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  up</span>dateStreaks(dt) {
<span class="cstat-no" title="statement not covered" >    for (let i = <span class="cstat-no" title="statement not covered" >this.streaks.length - 1;</span> i &gt;= 0; i--) {</span>
      const s = <span class="cstat-no" title="statement not covered" >this.streaks[i];</span>
<span class="cstat-no" title="statement not covered" >      s.r += s.speed * (dt / 1000);</span>
<span class="cstat-no" title="statement not covered" >      s.alpha *= 0.985;</span>
<span class="cstat-no" title="statement not covered" >      s.w *= 0.995;</span>
<span class="cstat-no" title="statement not covered" >      if (s.alpha &lt; 0.03) <span class="cstat-no" title="statement not covered" >this.streaks.splice(i, 1);</span></span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  dr</span>awStreaks(ctx) {
<span class="cstat-no" title="statement not covered" >    if (this.streaks.length === 0) <span class="cstat-no" title="statement not covered" >return;</span></span>
<span class="cstat-no" title="statement not covered" >    ctx.save();</span>
<span class="cstat-no" title="statement not covered" >    ctx.strokeStyle = this.theme.particleSecondary;</span>
<span class="cstat-no" title="statement not covered" >    for (const s of this.streaks) {</span>
<span class="cstat-no" title="statement not covered" >      ctx.globalAlpha = s.alpha;</span>
<span class="cstat-no" title="statement not covered" >      ctx.lineWidth = s.w;</span>
<span class="cstat-no" title="statement not covered" >      ctx.beginPath();</span>
<span class="cstat-no" title="statement not covered" >      ctx.arc(s.x, s.y, s.r, 0, Math.PI * 2);</span>
<span class="cstat-no" title="statement not covered" >      ctx.stroke();</span>
    }
<span class="cstat-no" title="statement not covered" >    ctx.restore();</span>
<span class="cstat-no" title="statement not covered" >    ctx.globalAlpha = 1.0;</span>
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  dr</span>awOverlay(ctx) {
    const { width, height } = <span class="cstat-no" title="statement not covered" >this.canvas;</span>
&nbsp;
    // Gradient overlay (distortion feel)
    const grad = <span class="cstat-no" title="statement not covered" >ctx.createRadialGradient(</span>
      width / 2, height / 2, Math.min(width, height) * 0.1,
      width / 2, height / 2, Math.max(width, height) * 0.7
    );
<span class="cstat-no" title="statement not covered" >    grad.addColorStop(0, this.theme.gradient[0]);</span>
<span class="cstat-no" title="statement not covered" >    grad.addColorStop(1, this.theme.gradient[1]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    ctx.save();</span>
<span class="cstat-no" title="statement not covered" >    ctx.globalAlpha = this.overlay.alpha;</span>
<span class="cstat-no" title="statement not covered" >    ctx.fillStyle = grad;</span>
<span class="cstat-no" title="statement not covered" >    ctx.fillRect(0, 0, width, height);</span>
&nbsp;
    // Vignette
<span class="cstat-no" title="statement not covered" >    if (this.vignette.strength &gt; 0.02) {</span>
      const vg = <span class="cstat-no" title="statement not covered" >ctx.createRadialGradient(width / 2, height / 2, Math.min(width, height) * 0.45,</span>
        width / 2, height / 2, Math.max(width, height) * 0.8);
<span class="cstat-no" title="statement not covered" >      vg.addColorStop(0, 'rgba(0,0,0,0)');</span>
<span class="cstat-no" title="statement not covered" >      vg.addColorStop(1, `rgba(0,0,0,${0.6 * this.vignette.strength})`);</span>
<span class="cstat-no" title="statement not covered" >      ctx.fillStyle = vg;</span>
<span class="cstat-no" title="statement not covered" >      ctx.fillRect(0, 0, width, height);</span>
    }
&nbsp;
    // Tint
<span class="cstat-no" title="statement not covered" >    if (this.tint.strength &gt; 0.02) {</span>
<span class="cstat-no" title="statement not covered" >      ctx.fillStyle = this.theme.tint;</span>
<span class="cstat-no" title="statement not covered" >      ctx.globalAlpha = this.tint.strength * 0.7;</span>
<span class="cstat-no" title="statement not covered" >      ctx.fillRect(0, 0, width, height);</span>
    }
&nbsp;
    // Scanlines / distortion lines
    const lines = <span class="cstat-no" title="statement not covered" >Math.floor(10 * this.lod);</span>
<span class="cstat-no" title="statement not covered" >    ctx.globalAlpha = 0.08 * this.overlay.alpha;</span>
<span class="cstat-no" title="statement not covered" >    ctx.fillStyle = '#ffffff';</span>
<span class="cstat-no" title="statement not covered" >    for (let i = <span class="cstat-no" title="statement not covered" >0;</span> i &lt; lines; i++) {</span>
      const y = (<span class="cstat-no" title="statement not covered" >(i * 37.3 + this.noise.time * 120) % height)</span>;
<span class="cstat-no" title="statement not covered" >      ctx.fillRect(0, y, width, 1);</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    ctx.restore();</span>
<span class="cstat-no" title="statement not covered" >    ctx.globalAlpha = 1.0;</span>
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  dr</span>awUI(ctx) {
    const pad = <span class="cstat-no" title="statement not covered" >12;</span>
    const w = <span class="cstat-no" title="statement not covered" >Math.min(360, this.canvas.width - pad * 2);</span>
    const x = <span class="cstat-no" title="statement not covered" >(this.canvas.width - w) / 2;</span>
    const y = <span class="cstat-no" title="statement not covered" >48;</span>
&nbsp;
    // Progress calculation
    let progress = <span class="cstat-no" title="statement not covered" >0;</span>
<span class="cstat-no" title="statement not covered" >    if (this.state === 'active') {</span>
<span class="cstat-no" title="statement not covered" >      progress = Math.min(1, this.currentWarp.elapsed / Math.max(1, this.currentWarp.duration));</span>
    } else <span class="cstat-no" title="statement not covered" >if (this.state === 'cooldown' &amp;&amp; this.currentWarp.cooldown &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >      progress = Math.min(1, this.currentWarp.elapsed / this.currentWarp.cooldown);</span>
    }
&nbsp;
    // Panel
<span class="cstat-no" title="statement not covered" >    ctx.save();</span>
<span class="cstat-no" title="statement not covered" >    ctx.globalAlpha = 0.9;</span>
<span class="cstat-no" title="statement not covered" >    ctx.fillStyle = 'rgba(0,0,0,0.45)';</span>
<span class="cstat-no" title="statement not covered" >    ctx.fillRect(x, y, w, 46);</span>
<span class="cstat-no" title="statement not covered" >    ctx.strokeStyle = 'rgba(255,255,255,0.15)';</span>
<span class="cstat-no" title="statement not covered" >    ctx.strokeRect(x, y, w, 46);</span>
&nbsp;
    // Bar bg
    const barY = <span class="cstat-no" title="statement not covered" >y + 28;</span>
<span class="cstat-no" title="statement not covered" >    ctx.fillStyle = 'rgba(255,255,255,0.08)';</span>
<span class="cstat-no" title="statement not covered" >    ctx.fillRect(x + 8, barY, w - 16, 8);</span>
&nbsp;
    // Bar fg
    const statusColor = <span class="cstat-no" title="statement not covered" >this.theme.particleSecondary;</span>
<span class="cstat-no" title="statement not covered" >    ctx.fillStyle = statusColor;</span>
<span class="cstat-no" title="statement not covered" >    ctx.fillRect(x + 8, barY, (w - 16) * progress, 8);</span>
&nbsp;
    // Text
<span class="cstat-no" title="statement not covered" >    ctx.globalAlpha = 1.0;</span>
<span class="cstat-no" title="statement not covered" >    ctx.fillStyle = '#ffffff';</span>
<span class="cstat-no" title="statement not covered" >    ctx.font = '12px Arial';</span>
<span class="cstat-no" title="statement not covered" >    ctx.textAlign = 'left';</span>
    const stateLabel = <span class="cstat-no" title="statement not covered" >this.state.replace('_', ' ');</span>
<span class="cstat-no" title="statement not covered" >    ctx.fillText(`Warp: ${this.currentWarp.type} - ${stateLabel}`, x + 10, y + 18);</span>
&nbsp;
    // Prompt snippet
<span class="cstat-no" title="statement not covered" >    if (this.currentWarp.promptSnippet &amp;&amp; this.state !== 'idle') {</span>
<span class="cstat-no" title="statement not covered" >      ctx.textAlign = 'right';</span>
<span class="cstat-no" title="statement not covered" >      ctx.fillStyle = 'rgba(255,255,255,0.8)';</span>
<span class="cstat-no" title="statement not covered" >      ctx.fillText(this.currentWarp.promptSnippet, x + w - 10, y + 18);</span>
    }
&nbsp;
    // Debug metrics
<span class="cstat-no" title="statement not covered" >    if (this.debug) {</span>
<span class="cstat-no" title="statement not covered" >      ctx.textAlign = 'left';</span>
<span class="cstat-no" title="statement not covered" >      ctx.fillStyle = 'rgba(255,255,255,0.7)';</span>
      const fps = <span class="cstat-no" title="statement not covered" >this.gameEngine ? this.gameEngine.currentFPS : 0;</span>
<span class="cstat-no" title="statement not covered" >      ctx.fillText(`FPS:${fps.toFixed ? fps.toFixed(0) : fps} Part:${this.metrics.particles} LOD:${this.lod.toFixed(2)}`,</span>
        x + 10, barY + 22);
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    ctx.restore();</span>
  }
&nbsp;
  // Audio --------------------------------------------------------------------
&nbsp;
<span class="fstat-no" title="function not covered" >  en</span>sureAudio() {
<span class="cstat-no" title="statement not covered" >    if (this.audioCtx) <span class="cstat-no" title="statement not covered" >return;</span></span>
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      this.audioCtx = new (window.AudioContext || window.webkitAudioContext)();</span>
    } catch (_) {
<span class="cstat-no" title="statement not covered" >      this.audioCtx = null; </span>// Audio not supported
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  pl</span>aySound(kind) {
<span class="cstat-no" title="statement not covered" >    this.ensureAudio();</span>
<span class="cstat-no" title="statement not covered" >    if (!this.audioCtx) <span class="cstat-no" title="statement not covered" >return;</span></span>
    const ctx = <span class="cstat-no" title="statement not covered" >this.audioCtx;</span>
    const o = <span class="cstat-no" title="statement not covered" >ctx.createOscillator();</span>
    const g = <span class="cstat-no" title="statement not covered" >ctx.createGain();</span>
<span class="cstat-no" title="statement not covered" >    o.type = kind === 'error' ? 'square' : kind === 'complete' ? 'triangle' : 'sine';</span>
    const base = <span class="cstat-no" title="statement not covered" >this.theme.sound.base;</span>
<span class="cstat-no" title="statement not covered" >    o.frequency.value = kind === 'init' ? base * 1.5 : kind === 'complete' ? base * 0.75 : base * 0.5;</span>
<span class="cstat-no" title="statement not covered" >    g.gain.value = 0.0001;</span>
<span class="cstat-no" title="statement not covered" >    o.connect(g).connect(ctx.destination);</span>
<span class="cstat-no" title="statement not covered" >    o.start();</span>
    // Envelope
    const now = <span class="cstat-no" title="statement not covered" >ctx.currentTime;</span>
    const peak = <span class="cstat-no" title="statement not covered" >kind === 'init' ? 0.2 : kind === 'complete' ? 0.15 : 0.12;</span>
<span class="cstat-no" title="statement not covered" >    g.gain.exponentialRampToValueAtTime(peak, now + 0.03);</span>
<span class="cstat-no" title="statement not covered" >    g.gain.exponentialRampToValueAtTime(0.0001, now + 0.25);</span>
<span class="cstat-no" title="statement not covered" >    o.stop(now + 0.3);</span>
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  st</span>artAmbient() {
<span class="cstat-no" title="statement not covered" >    this.ensureAudio();</span>
<span class="cstat-no" title="statement not covered" >    if (!this.audioCtx) <span class="cstat-no" title="statement not covered" >return;</span></span>
<span class="cstat-no" title="statement not covered" >    if (this.ambientNode) <span class="cstat-no" title="statement not covered" >return;</span></span>
    const ctx = <span class="cstat-no" title="statement not covered" >this.audioCtx;</span>
    const o = <span class="cstat-no" title="statement not covered" >ctx.createOscillator();</span>
    const g = <span class="cstat-no" title="statement not covered" >ctx.createGain();</span>
<span class="cstat-no" title="statement not covered" >    o.type = 'sawtooth';</span>
<span class="cstat-no" title="statement not covered" >    o.frequency.value = this.theme.sound.base * 0.33;</span>
<span class="cstat-no" title="statement not covered" >    g.gain.value = 0.0001;</span>
<span class="cstat-no" title="statement not covered" >    o.connect(g).connect(ctx.destination);</span>
<span class="cstat-no" title="statement not covered" >    o.start();</span>
    const now = <span class="cstat-no" title="statement not covered" >ctx.currentTime;</span>
<span class="cstat-no" title="statement not covered" >    g.gain.exponentialRampToValueAtTime(0.03, now + 0.2);</span>
<span class="cstat-no" title="statement not covered" >    this.ambientNode = { o, g };</span>
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  st</span>opAmbient() {
<span class="cstat-no" title="statement not covered" >    if (!this.ambientNode || !this.audioCtx) <span class="cstat-no" title="statement not covered" >return;</span></span>
    const { o, g } = <span class="cstat-no" title="statement not covered" >this.ambientNode;</span>
    const now = <span class="cstat-no" title="statement not covered" >this.audioCtx.currentTime;</span>
<span class="cstat-no" title="statement not covered" >    g.gain.exponentialRampToValueAtTime(0.0001, now + 0.2);</span>
<span class="cstat-no" title="statement not covered" >    o.stop(now + 0.25);</span>
<span class="cstat-no" title="statement not covered" >    this.ambientNode = null;</span>
  }
}
&nbsp;
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-08T03:37:35.475Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    