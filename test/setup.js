// Global setup for Jest tests
import { jest } from '@jest/globals';

// Make jest globally available
global.jest = jest;

// Mock the Canvas API for tests that don't need actual rendering
global.HTMLCanvasElement = class HTMLCanvasElement {
  constructor() {
    this.width = 800;
    this.height = 600;
  }
  
  getContext() {
    return {
      fillStyle: '',
      strokeStyle: '',
      lineWidth: 1,
      font: '',
      textAlign: 'left',
      textBaseline: 'top',
      beginPath: () => {},
      closePath: () => {},
      moveTo: () => {},
      lineTo: () => {},
      arc: () => {},
      fill: () => {},
      stroke: () => {},
      fillRect: () => {},
      strokeRect: () => {},
      clearRect: () => {},
      save: () => {},
      restore: () => {},
      translate: () => {},
      rotate: () => {},
      scale: () => {},
      drawImage: () => {},
      fillText: () => {},
      measureText: () => ({ width: 100 }),
      globalAlpha: 1
    };
  }
};

// Mock Image class
global.HTMLImageElement = class HTMLImageElement {
  constructor() {
    this.width = 100;
    this.height = 100;
    this.src = '';
    this.onload = null;
    this.onerror = null;
  }
};

// Mock performance API
global.performance = {
  now: () => Date.now()
};

// Mock console methods to reduce noise during tests
global.console = {
  ...console,
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

// Set up global test utilities
global.createMockGameObject = () => ({
  position: { x: 0, y: 0 },
  velocity: { x: 0, y: 0 },
  active: true,
  visible: true,
  update: jest.fn(),
  render: jest.fn(),
  destroy: jest.fn(),
  reset: jest.fn()
});

global.createMockPlayerShip = () => ({
  ...global.createMockGameObject(),
  health: 100,
  maxHealth: 100,
  lives: 3,
  weaponSystem: {
    enableSpreadPattern: jest.fn(),
    gameObjectManager: {
      add: jest.fn()
    }
  },
  addLives: jest.fn(),
  takeDamage: jest.fn()
});

global.createMockVector2 = (x = 0, y = 0) => ({
  x,
  y,
  add: jest.fn().mockReturnThis(),
  subtract: jest.fn().mockReturnThis(),
  multiply: jest.fn().mockReturnThis(),
  divide: jest.fn().mockReturnThis(),
  magnitude: jest.fn().mockReturnValue(Math.sqrt(x * x + y * y)),
  normalize: jest.fn().mockReturnThis(),
  distanceTo: jest.fn().mockReturnValue(0),
  angle: jest.fn().mockReturnValue(0)
});