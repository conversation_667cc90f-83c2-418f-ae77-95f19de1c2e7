import { jest } from '@jest/globals';
import {
    PowerUp,
    ExtraLifePowerUp,
    SpreadAmmoPowerUp,
    ExtraWingmanPowerUp,
    RealityWarpPowerUp,
    PowerUpFactory
} from '../../src/systems/PowerUp.js';
import { GAME_CONFIG } from '../../src/config/gameConfig.js';
import { Vector2 } from '../../src/utils/Vector2.js';

// Mock PlayerShip for testing
class MockPlayerShip {
    constructor() {
        this.lives = 3;
        this.weaponSystem = {
            enableSpreadPattern: jest.fn(),
            gameObjectManager: {
                add: jest.fn()
            }
        };
        this.position = new Vector2(100, 100);
        this.realityWarpTier = null;
    }

    addLives(count) {
        this.lives += count;
    }
}

// Mock WingmanShip for testing
class MockWingmanShip {
    constructor(x, y, playerShip, gameObjectManager) {
        this.x = x;
        this.y = y;
        this.playerShip = playerShip;
        this.gameObjectManager = gameObjectManager;
        this.destroyed = false;
    }

    destroy() {
        this.destroyed = true;
    }
}

// Mock GAME_CONFIG
jest.mock('../../src/config/gameConfig.js', () => ({
    GAME_CONFIG: {
        POWER_UP_COSTS: {
            EXTRA_LIFE: 100,
            SPREAD_AMMO: 150,
            EXTRA_WINGMAN: 300,
            REALITY_WARP_BASIC: 500,
            REALITY_WARP_ADVANCED: 1000,
            REALITY_WARP_ULTIMATE: 2000
        },
        BASE_LEVEL_REWARD: 50
    }
}));

// Mock Vector2
jest.mock('../../src/utils/Vector2.js', () => ({
    Vector2: jest.fn().mockImplementation((x, y) => ({
        x,
        y,
        add: jest.fn().mockReturnThis(),
        subtract: jest.fn().mockReturnThis(),
        multiply: jest.fn().mockReturnThis(),
        normalize: jest.fn().mockReturnThis(),
        length: jest.fn().mockReturnValue(1),
        distanceTo: jest.fn().mockReturnValue(1)
    }))
}));

// Mock WingmanShip import
jest.mock('../../src/entities/WingmanShip.js', () => ({
    WingmanShip: MockWingmanShip
}));

describe('PowerUp', () => {
    let powerUp;
    let mockPlayerShip;

    beforeEach(() => {
        // Reset static counter
        PowerUp.idCounter = 0;
        
        powerUp = new PowerUp('TEST_POWER', 100, 5000, 'Test power-up');
        mockPlayerShip = new MockPlayerShip();
        
        // Clear console mocks
        jest.clearAllMocks();
    });

    describe('Constructor', () => {
        test('should initialize with correct values', () => {
            expect(powerUp.type).toBe('TEST_POWER');
            expect(powerUp.cost).toBe(100);
            expect(powerUp.duration).toBe(5000);
            expect(powerUp.description).toBe('Test power-up');
            expect(powerUp.isActive).toBe(false);
            expect(powerUp.timeRemaining).toBe(5000);
            expect(powerUp.appliedAt).toBeNull();
            expect(powerUp.id).toMatch(/^powerup_\d+$/);
            expect(powerUp.icon).toBeNull();
            expect(powerUp.color).toBe('#00ffff');
            expect(powerUp.glowColor).toBe('#ffffff');
        });

        test('should generate unique IDs', () => {
            const powerUp1 = new PowerUp('TEST', 100);
            const powerUp2 = new PowerUp('TEST', 100);
            
            expect(powerUp1.id).not.toBe(powerUp2.id);
        });

        test('should handle null duration for permanent power-ups', () => {
            const permanentPowerUp = new PowerUp('PERMANENT', 100, null);
            
            expect(permanentPowerUp.duration).toBeNull();
            expect(permanentPowerUp.timeRemaining).toBeNull();
        });
    });

    describe('apply', () => {
        test('should apply power-up successfully', async () => {
            powerUp.applyEffect = jest.fn().mockResolvedValue(true);
            
            const result = await powerUp.apply(mockPlayerShip);
            
            expect(result).toBe(true);
            expect(powerUp.isActive).toBe(true);
            expect(powerUp.appliedAt).not.toBeNull();
            expect(powerUp.timeRemaining).toBe(5000);
            expect(powerUp.applyEffect).toHaveBeenCalledWith(mockPlayerShip);
        });

        test('should not apply if already active', async () => {
            powerUp.isActive = true;
            
            const result = await powerUp.apply(mockPlayerShip);
            
            expect(result).toBe(false);
            expect(powerUp.applyEffect).not.toHaveBeenCalled();
        });

        test('should reset state if application fails', async () => {
            powerUp.applyEffect = jest.fn().mockResolvedValue(false);
            
            const result = await powerUp.apply(mockPlayerShip);
            
            expect(result).toBe(false);
            expect(powerUp.isActive).toBe(false);
            expect(powerUp.appliedAt).toBeNull();
            expect(powerUp.timeRemaining).toBe(5000);
        });

        test('should handle synchronous applyEffect', async () => {
            powerUp.applyEffect = jest.fn().mockReturnValue(true);
            
            const result = await powerUp.apply(mockPlayerShip);
            
            expect(result).toBe(true);
            expect(powerUp.isActive).toBe(true);
        });
    });

    describe('remove', () => {
        beforeEach(() => {
            powerUp.isActive = true;
            powerUp.appliedAt = Date.now();
            powerUp.timeRemaining = 3000;
        });

        test('should remove power-up successfully', () => {
            powerUp.removeEffect = jest.fn().mockReturnValue(true);
            
            const result = powerUp.remove(mockPlayerShip);
            
            expect(result).toBe(true);
            expect(powerUp.isActive).toBe(false);
            expect(powerUp.timeRemaining).toBe(0);
            expect(powerUp.removeEffect).toHaveBeenCalledWith(mockPlayerShip);
        });

        test('should not remove if not active', () => {
            powerUp.isActive = false;
            
            const result = powerUp.remove(mockPlayerShip);
            
            expect(result).toBe(false);
            expect(powerUp.removeEffect).not.toHaveBeenCalled();
        });
    });

    describe('update', () => {
        beforeEach(() => {
            powerUp.isActive = true;
            powerUp.timeRemaining = 5000;
        });

        test('should update time remaining for temporary power-up', () => {
            const deltaTime = 1000;
            
            const result = powerUp.update(deltaTime, mockPlayerShip);
            
            expect(result).toBe(true);
            expect(powerUp.timeRemaining).toBe(4000);
        });

        test('should auto-remove when time expires', () => {
            powerUp.timeRemaining = 500;
            powerUp.remove = jest.fn();
            
            const deltaTime = 1000;
            
            const result = powerUp.update(deltaTime, mockPlayerShip);
            
            expect(result).toBe(false);
            expect(powerUp.remove).toHaveBeenCalledWith(mockPlayerShip);
        });

        test('should not update if not active', () => {
            powerUp.isActive = false;
            
            const result = powerUp.update(1000, mockPlayerShip);
            
            expect(result).toBe(false);
            expect(powerUp.timeRemaining).toBe(5000);
        });

        test('should handle permanent power-ups', () => {
            powerUp.duration = null;
            powerUp.timeRemaining = null;
            
            const deltaTime = 1000;
            
            const result = powerUp.update(deltaTime, mockPlayerShip);
            
            expect(result).toBe(true);
            expect(powerUp.timeRemaining).toBeNull();
        });
    });

    describe('getTimeRemainingPercentage', () => {
        test('should return 1 for permanent power-ups', () => {
            powerUp.duration = null;
            
            const percentage = powerUp.getTimeRemainingPercentage();
            
            expect(percentage).toBe(1);
        });

        test('should return 0 for inactive power-ups', () => {
            powerUp.isActive = false;
            
            const percentage = powerUp.getTimeRemainingPercentage();
            
            expect(percentage).toBe(0);
        });

        test('should calculate correct percentage for active power-ups', () => {
            powerUp.isActive = true;
            powerUp.duration = 10000;
            powerUp.timeRemaining = 5000;
            
            const percentage = powerUp.getTimeRemainingPercentage();
            
            expect(percentage).toBe(0.5);
        });

        test('should return 0 when time remaining is negative', () => {
            powerUp.isActive = true;
            powerUp.duration = 10000;
            powerUp.timeRemaining = -1000;
            
            const percentage = powerUp.getTimeRemainingPercentage();
            
            expect(percentage).toBe(0);
        });
    });

    describe('getFormattedTimeRemaining', () => {
        test('should return "Permanent" for permanent power-ups', () => {
            powerUp.duration = null;
            
            const formatted = powerUp.getFormattedTimeRemaining();
            
            expect(formatted).toBe('Permanent');
        });

        test('should return "Inactive" for inactive power-ups', () => {
            powerUp.isActive = false;
            
            const formatted = powerUp.getFormattedTimeRemaining();
            
            expect(formatted).toBe('Inactive');
        });

        test('should return seconds for active power-ups', () => {
            powerUp.isActive = true;
            powerUp.duration = 10000;
            powerUp.timeRemaining = 5500;
            
            const formatted = powerUp.getFormattedTimeRemaining();
            
            expect(formatted).toBe('6s'); // 5500ms rounds up to 6s
        });

        test('should return 1s for very small remaining time', () => {
            powerUp.isActive = true;
            powerUp.duration = 10000;
            powerUp.timeRemaining = 100;
            
            const formatted = powerUp.getFormattedTimeRemaining();
            
            expect(formatted).toBe('1s');
        });
    });

    describe('canPurchase', () => {
        test('should allow purchase with sufficient tokens and not active', () => {
            const result = powerUp.canPurchase(mockPlayerShip, 200);
            
            expect(result.canPurchase).toBe(true);
            expect(result.reason).toBe('available');
        });

        test('should reject purchase with insufficient tokens', () => {
            const result = powerUp.canPurchase(mockPlayerShip, 50);
            
            expect(result.canPurchase).toBe(false);
            expect(result.reason).toBe('insufficient_tokens');
        });

        test('should reject purchase when already active', () => {
            powerUp.isActive = true;
            
            const result = powerUp.canPurchase(mockPlayerShip, 200);
            
            expect(result.canPurchase).toBe(false);
            expect(result.reason).toBe('already_active');
        });
    });

    describe('getDisplayInfo', () => {
        beforeEach(() => {
            powerUp.isActive = true;
            powerUp.timeRemaining = 3000;
        });

        test('should return complete display information', () => {
            const info = powerUp.getDisplayInfo();
            
            expect(info).toHaveProperty('id');
            expect(info).toHaveProperty('type');
            expect(info).toHaveProperty('cost');
            expect(info).toHaveProperty('duration');
            expect(info).toHaveProperty('description');
            expect(info).toHaveProperty('isActive');
            expect(info).toHaveProperty('timeRemaining');
            expect(info).toHaveProperty('timeRemainingPercentage');
            expect(info).toHaveProperty('formattedTimeRemaining');
            expect(info).toHaveProperty('icon');
            expect(info).toHaveProperty('color');
            expect(info).toHaveProperty('glowColor');
            
            expect(info.type).toBe('TEST_POWER');
            expect(info.cost).toBe(100);
            expect(info.isActive).toBe(true);
            expect(info.timeRemaining).toBe(3000);
        });

        test('should calculate derived values correctly', () => {
            powerUp.duration = 10000;
            powerUp.timeRemaining = 3000;
            
            const info = powerUp.getDisplayInfo();
            
            expect(info.timeRemainingPercentage).toBe(0.3);
            expect(info.formattedTimeRemaining).toBe('3s');
        });
    });
});

describe('ExtraLifePowerUp', () => {
    let powerUp;
    let mockPlayerShip;

    beforeEach(() => {
        powerUp = new ExtraLifePowerUp();
        mockPlayerShip = new MockPlayerShip();
        mockPlayerShip.lives = 3;
    });

    describe('Constructor', () => {
        test('should initialize with correct values', () => {
            expect(powerUp.type).toBe('EXTRA_LIFE');
            expect(powerUp.cost).toBe(GAME_CONFIG.POWER_UP_COSTS.EXTRA_LIFE);
            expect(powerUp.duration).toBeNull();
            expect(powerUp.description).toBe('Gain an extra life to continue your journey');
            expect(powerUp.icon).toBe('❤️');
            expect(powerUp.color).toBe('#ff4444');
            expect(powerUp.glowColor).toBe('#ff8888');
        });
    });

    describe('applyEffect', () => {
        test('should add one life to player ship', () => {
            const result = powerUp.applyEffect(mockPlayerShip);
            
            expect(result).toBe(true);
            expect(mockPlayerShip.lives).toBe(4);
        });
    });

    describe('canPurchase', () => {
        test('should allow purchase with sufficient tokens regardless of active state', () => {
            powerUp.isActive = true;
            
            const result = powerUp.canPurchase(mockPlayerShip, 200);
            
            expect(result.canPurchase).toBe(true);
            expect(result.reason).toBe('available');
        });

        test('should reject purchase with insufficient tokens', () => {
            const result = powerUp.canPurchase(mockPlayerShip, 50);
            
            expect(result.canPurchase).toBe(false);
            expect(result.reason).toBe('insufficient_tokens');
        });
    });
});

describe('SpreadAmmoPowerUp', () => {
    let powerUp;
    let mockPlayerShip;

    beforeEach(() => {
        powerUp = new SpreadAmmoPowerUp();
        mockPlayerShip = new MockPlayerShip();
    });

    describe('Constructor', () => {
        test('should initialize with correct values', () => {
            expect(powerUp.type).toBe('SPREAD_AMMO');
            expect(powerUp.cost).toBe(GAME_CONFIG.POWER_UP_COSTS.SPREAD_AMMO);
            expect(powerUp.duration).toBe(30000);
            expect(powerUp.description).toBe('Fire projectiles in a spread pattern for better coverage');
            expect(powerUp.icon).toBe('🔥');
            expect(powerUp.color).toBe('#ffaa00');
            expect(powerUp.glowColor).toBe('#ffdd44');
        });
    });

    describe('applyEffect', () => {
        test('should enable spread pattern on weapon system', () => {
            const result = powerUp.applyEffect(mockPlayerShip);
            
            expect(result).toBe(true);
            expect(mockPlayerShip.weaponSystem.enableSpreadPattern).toHaveBeenCalledWith(true);
        });

        test('should return false if no weapon system', () => {
            mockPlayerShip.weaponSystem = null;
            
            const result = powerUp.applyEffect(mockPlayerShip);
            
            expect(result).toBe(false);
        });
    });

    describe('removeEffect', () => {
        test('should disable spread pattern on weapon system', () => {
            const result = powerUp.removeEffect(mockPlayerShip);
            
            expect(result).toBe(true);
            expect(mockPlayerShip.weaponSystem.enableSpreadPattern).toHaveBeenCalledWith(false);
        });

        test('should return false if no weapon system', () => {
            mockPlayerShip.weaponSystem = null;
            
            const result = powerUp.removeEffect(mockPlayerShip);
            
            expect(result).toBe(false);
        });
    });
});

describe('ExtraWingmanPowerUp', () => {
    let powerUp;
    let mockPlayerShip;

    beforeEach(() => {
        powerUp = new ExtraWingmanPowerUp();
        mockPlayerShip = new MockPlayerShip();
    });

    describe('Constructor', () => {
        test('should initialize with correct values', () => {
            expect(powerUp.type).toBe('EXTRA_WINGMAN');
            expect(powerUp.cost).toBe(GAME_CONFIG.POWER_UP_COSTS.EXTRA_WINGMAN);
            expect(powerUp.duration).toBe(45000);
            expect(powerUp.description).toBe('Deploy a wingman ship that provides covering fire');
            expect(powerUp.icon).toBe('🚀');
            expect(powerUp.color).toBe('#00ff88');
            expect(powerUp.glowColor).toBe('#44ffaa');
            expect(powerUp.wingmanShip).toBeNull();
        });
    });

    describe('applyEffect', () => {
        test('should create and add wingman ship', async () => {
            const result = await powerUp.applyEffect(mockPlayerShip);
            
            expect(result).toBe(true);
            expect(powerUp.wingmanShip).toBeInstanceOf(MockWingmanShip);
            expect(mockPlayerShip.weaponSystem.gameObjectManager.add).toHaveBeenCalledWith(powerUp.wingmanShip);
        });

        test('should return false if no game object manager', async () => {
            mockPlayerShip.weaponSystem.gameObjectManager = null;
            
            const result = await powerUp.applyEffect(mockPlayerShip);
            
            expect(result).toBe(false);
            expect(powerUp.wingmanShip).toBeNull();
        });

        test('should position wingman correctly', async () => {
            const result = await powerUp.applyEffect(mockPlayerShip);
            
            expect(result).toBe(true);
            expect(powerUp.wingmanShip.x).toBe(60); // 100 - 40
            expect(powerUp.wingmanShip.y).toBe(100);
        });
    });

    describe('removeEffect', () => {
        beforeEach(async () => {
            await powerUp.applyEffect(mockPlayerShip);
        });

        test('should destroy wingman ship', () => {
            const result = powerUp.removeEffect(mockPlayerShip);
            
            expect(result).toBe(true);
            expect(powerUp.wingmanShip.destroyed).toBe(true);
            expect(powerUp.wingmanShip).toBeNull();
        });

        test('should handle null wingman ship', () => {
            powerUp.wingmanShip = null;
            
            const result = powerUp.removeEffect(mockPlayerShip);
            
            expect(result).toBe(true);
        });
    });
});

describe('RealityWarpPowerUp', () => {
    let powerUp;
    let mockPlayerShip;

    describe('Basic tier', () => {
        beforeEach(() => {
            powerUp = new RealityWarpPowerUp('basic');
            mockPlayerShip = new MockPlayerShip();
        });

        describe('Constructor', () => {
            test('should initialize with correct values for basic tier', () => {
                expect(powerUp.type).toBe('REALITY_WARP_BASIC');
                expect(powerUp.cost).toBe(GAME_CONFIG.POWER_UP_COSTS.REALITY_WARP_BASIC);
                expect(powerUp.duration).toBeNull();
                expect(powerUp.description).toBe('Transform the next level with your imagination');
                expect(powerUp.icon).toBe('🌌');
                expect(powerUp.color).toBe('#6366f1');
                expect(powerUp.glowColor).toBe('#818cf8');
                expect(powerUp.tier).toBe('basic');
            });
        });
    });

    describe('Advanced tier', () => {
        beforeEach(() => {
            powerUp = new RealityWarpPowerUp('advanced');
            mockPlayerShip = new MockPlayerShip();
        });

        describe('Constructor', () => {
            test('should initialize with correct values for advanced tier', () => {
                expect(powerUp.type).toBe('REALITY_WARP_ADVANCED');
                expect(powerUp.cost).toBe(GAME_CONFIG.POWER_UP_COSTS.REALITY_WARP_ADVANCED);
                expect(powerUp.icon).toBe('✨');
                expect(powerUp.color).toBe('#a855f7');
                expect(powerUp.glowColor).toBe('#c084fc');
                expect(powerUp.tier).toBe('advanced');
            });
        });
    });

    describe('Ultimate tier', () => {
        beforeEach(() => {
            powerUp = new RealityWarpPowerUp('ultimate');
            mockPlayerShip = new MockPlayerShip();
        });

        describe('Constructor', () => {
            test('should initialize with correct values for ultimate tier', () => {
                expect(powerUp.type).toBe('REALITY_WARP_ULTIMATE');
                expect(powerUp.cost).toBe(GAME_CONFIG.POWER_UP_COSTS.REALITY_WARP_ULTIMATE);
                expect(powerUp.icon).toBe('🔮');
                expect(powerUp.color).toBe('#ec4899');
                expect(powerUp.glowColor).toBe('#f472b6');
                expect(powerUp.tier).toBe('ultimate');
            });
        });
    });

    describe('Invalid tier', () => {
        test('should default to basic tier for invalid tier', () => {
            const powerUp = new RealityWarpPowerUp('invalid');
            
            expect(powerUp.type).toBe('REALITY_WARP_BASIC');
            expect(powerUp.tier).toBe('basic');
        });
    });

    describe('apply', () => {
        beforeEach(() => {
            powerUp = new RealityWarpPowerUp('advanced');
            mockPlayerShip = new MockPlayerShip();
        });

        test('should set warp tier on player ship', async () => {
            const result = await powerUp.apply(mockPlayerShip);
            
            expect(result).toBe(true);
            expect(powerUp.isActive).toBe(true);
            expect(mockPlayerShip.realityWarpTier).toBe('advanced');
        });

        test('should not apply if already active', async () => {
            powerUp.isActive = true;
            
            const result = await powerUp.apply(mockPlayerShip);
            
            expect(result).toBe(false);
            expect(mockPlayerShip.realityWarpTier).toBeNull();
        });
    });

    describe('remove', () => {
        beforeEach(() => {
            powerUp = new RealityWarpPowerUp('ultimate');
            mockPlayerShip = new MockPlayerShip();
        });

        test('should clear warp tier from player ship', () => {
            powerUp.isActive = true;
            mockPlayerShip.realityWarpTier = 'ultimate';
            
            const result = powerUp.remove(mockPlayerShip);
            
            expect(result).toBe(true);
            expect(powerUp.isActive).toBe(false);
            expect(mockPlayerShip.realityWarpTier).toBeNull();
        });

        test('should not clear different warp tier', () => {
            powerUp.isActive = true;
            mockPlayerShip.realityWarpTier = 'basic';
            
            const result = powerUp.remove(mockPlayerShip);
            
            expect(result).toBe(true);
            expect(powerUp.isActive).toBe(false);
            expect(mockPlayerShip.realityWarpTier).toBe('basic'); // Should remain unchanged
        });
    });

    describe('getDisplayInfo', () => {
        test('should include tier information', () => {
            const powerUp = new RealityWarpPowerUp('advanced');
            const info = powerUp.getDisplayInfo();
            
            expect(info).toHaveProperty('tier');
            expect(info).toHaveProperty('tierName');
            expect(info.tier).toBe('advanced');
            expect(info.tierName).toBe('Advanced');
        });
    });
});

describe('PowerUpFactory', () => {
    describe('createPowerUp', () => {
        test('should create ExtraLifePowerUp for EXTRA_LIFE type', () => {
            const powerUp = PowerUpFactory.createPowerUp('EXTRA_LIFE');
            
            expect(powerUp).toBeInstanceOf(ExtraLifePowerUp);
            expect(powerUp.type).toBe('EXTRA_LIFE');
        });

        test('should create SpreadAmmoPowerUp for SPREAD_AMMO type', () => {
            const powerUp = PowerUpFactory.createPowerUp('SPREAD_AMMO');
            
            expect(powerUp).toBeInstanceOf(SpreadAmmoPowerUp);
            expect(powerUp.type).toBe('SPREAD_AMMO');
        });

        test('should create ExtraWingmanPowerUp for EXTRA_WINGMAN type', () => {
            const powerUp = PowerUpFactory.createPowerUp('EXTRA_WINGMAN');
            
            expect(powerUp).toBeInstanceOf(ExtraWingmanPowerUp);
            expect(powerUp.type).toBe('EXTRA_WINGMAN');
        });

        test('should create RealityWarpPowerUp with correct tier for REALITY_WARP types', () => {
            const basic = PowerUpFactory.createPowerUp('REALITY_WARP_BASIC');
            const advanced = PowerUpFactory.createPowerUp('REALITY_WARP_ADVANCED');
            const ultimate = PowerUpFactory.createPowerUp('REALITY_WARP_ULTIMATE');
            
            expect(basic).toBeInstanceOf(RealityWarpPowerUp);
            expect(basic.tier).toBe('basic');
            
            expect(advanced).toBeInstanceOf(RealityWarpPowerUp);
            expect(advanced.tier).toBe('advanced');
            
            expect(ultimate).toBeInstanceOf(RealityWarpPowerUp);
            expect(ultimate.tier).toBe('ultimate');
        });

        test('should throw error for unknown power-up type', () => {
            expect(() => {
                PowerUpFactory.createPowerUp('UNKNOWN_TYPE');
            }).toThrow('Unknown power-up type: UNKNOWN_TYPE');
        });
    });

    describe('getAllPowerUpTypes', () => {
        test('should return all power-up types', () => {
            const types = PowerUpFactory.getAllPowerUpTypes();
            
            expect(types).toEqual([
                'EXTRA_LIFE',
                'SPREAD_AMMO',
                'EXTRA_WINGMAN',
                'REALITY_WARP_BASIC',
                'REALITY_WARP_ADVANCED',
                'REALITY_WARP_ULTIMATE'
            ]);
        });
    });

    describe('createAllPowerUps', () => {
        test('should create instance of each power-up type', () => {
            const powerUps = PowerUpFactory.createAllPowerUps();
            
            expect(powerUps).toHaveLength(6);
            expect(powerUps[0]).toBeInstanceOf(ExtraLifePowerUp);
            expect(powerUps[1]).toBeInstanceOf(SpreadAmmoPowerUp);
            expect(powerUps[2]).toBeInstanceOf(ExtraWingmanPowerUp);
            expect(powerUps[3]).toBeInstanceOf(RealityWarpPowerUp);
            expect(powerUps[3].tier).toBe('basic');
            expect(powerUps[4]).toBeInstanceOf(RealityWarpPowerUp);
            expect(powerUps[4].tier).toBe('advanced');
            expect(powerUps[5]).toBeInstanceOf(RealityWarpPowerUp);
            expect(powerUps[5].tier).toBe('ultimate');
        });
    });
});

// Additional tests for comprehensive coverage
describe('PowerUp Additional Tests', () => {
    let powerUp;
    let mockPlayerShip;

    beforeEach(() => {
        powerUp = new PowerUp('TEST_POWER', 100, 5000, 'Test power-up');
        mockPlayerShip = new MockPlayerShip();
        jest.clearAllMocks();
    });

    describe('PowerUp edge cases', () => {
        test('should handle negative deltaTime in update', () => {
            powerUp.isActive = true;
            powerUp.timeRemaining = 5000;
            
            const result = powerUp.update(-1000, mockPlayerShip);
            
            expect(result).toBe(true);
            expect(powerUp.timeRemaining).toBe(6000); // Should increase with negative deltaTime
        });

        test('should handle zero deltaTime in update', () => {
            powerUp.isActive = true;
            powerUp.timeRemaining = 5000;
            
            const result = powerUp.update(0, mockPlayerShip);
            
            expect(result).toBe(true);
            expect(powerUp.timeRemaining).toBe(5000); // Should remain unchanged
        });

        test('should handle very large deltaTime in update', () => {
            powerUp.isActive = true;
            powerUp.timeRemaining = 5000;
            powerUp.remove = jest.fn();
            
            const result = powerUp.update(10000, mockPlayerShip);
            
            expect(result).toBe(false);
            expect(powerUp.remove).toHaveBeenCalledWith(mockPlayerShip);
        });

        test('should generate unique IDs even with rapid creation', () => {
            const ids = new Set();
            for (let i = 0; i < 1000; i++) {
                const powerUp = new PowerUp('TEST', 100);
                ids.add(powerUp.id);
            }
            
            expect(ids.size).toBe(1000); // All IDs should be unique
        });

        test('should handle ID counter reset', () => {
            PowerUp.idCounter = Number.MAX_SAFE_INTEGER - 1;
            
            const powerUp1 = new PowerUp('TEST', 100);
            const powerUp2 = new PowerUp('TEST', 100);
            
            expect(powerUp1.id).toMatch(/^powerup_\d+$/);
            expect(powerUp2.id).toMatch(/^powerup_\d+$/);
            expect(powerUp1.id).not.toBe(powerUp2.id);
        });
    });

    describe('ExtraLifePowerUp additional tests', () => {
        let extraLifePowerUp;

        beforeEach(() => {
            extraLifePowerUp = new ExtraLifePowerUp();
            mockPlayerShip.lives = 3;
        });

        test('should handle negative lives', () => {
            mockPlayerShip.lives = -1;
            
            extraLifePowerUp.applyEffect(mockPlayerShip);
            
            expect(mockPlayerShip.lives).toBe(0);
        });

        test('should handle very large lives count', () => {
            mockPlayerShip.lives = Number.MAX_SAFE_INTEGER;
            
            extraLifePowerUp.applyEffect(mockPlayerShip);
            
            // Should handle overflow gracefully
            expect(mockPlayerShip.lives).toBeGreaterThan(0);
        });

        test('should work with custom addLives implementation', () => {
            mockPlayerShip.addLives = jest.fn().mockImplementation((count) => {
                mockPlayerShip.lives += count * 2; // Custom implementation
            });
            
            extraLifePowerUp.applyEffect(mockPlayerShip);
            
            expect(mockPlayerShip.addLives).toHaveBeenCalledWith(1);
            expect(mockPlayerShip.lives).toBe(5); // 3 + (1 * 2)
        });
    });

    describe('SpreadAmmoPowerUp additional tests', () => {
        let spreadAmmoPowerUp;

        beforeEach(() => {
            spreadAmmoPowerUp = new SpreadAmmoPowerUp();
        });

        test('should handle weapon system with no enableSpreadPattern method', () => {
            mockPlayerShip.weaponSystem = {};
            
            const result = spreadAmmoPowerUp.applyEffect(mockPlayerShip);
            
            expect(result).toBe(false);
        });

        test('should handle weapon system with non-function enableSpreadPattern', () => {
            mockPlayerShip.weaponSystem = {
                enableSpreadPattern: 'not a function'
            };
            
            const result = spreadAmmoPowerUp.applyEffect(mockPlayerShip);
            
            expect(result).toBe(false);
        });

        test('should handle enableSpreadPattern throwing an exception', () => {
            mockPlayerShip.weaponSystem = {
                enableSpreadPattern: jest.fn().mockImplementation(() => {
                    throw new Error('Test error');
                })
            };
            
            const result = spreadAmmoPowerUp.applyEffect(mockPlayerShip);
            
            expect(result).toBe(false);
        });

        test('should handle removeEffect with non-function enableSpreadPattern', () => {
            mockPlayerShip.weaponSystem = {};
            
            const result = spreadAmmoPowerUp.removeEffect(mockPlayerShip);
            
            expect(result).toBe(false);
        });
    });

    describe('ExtraWingmanPowerUp additional tests', () => {
        let wingmanPowerUp;

        beforeEach(() => {
            wingmanPowerUp = new ExtraWingmanPowerUp();
        });

        test('should handle WingmanShip import failure', async () => {
            // Mock import to fail
            jest.mock('../../src/entities/WingmanShip.js', () => {
                throw new Error('Import failed');
            });
            
            // Reset the import cache
            jest.resetModules();
            
            // This test would need to be run in isolation due to module caching
            // For now, we'll test the error handling path
            const originalApplyEffect = wingmanPowerUp.applyEffect;
            wingmanPowerUp.applyEffect = jest.fn().mockImplementation(async () => {
                try {
                    throw new Error('Import failed');
                } catch (error) {
                    console.error('Failed to create wingman ship:', error);
                    return false;
                }
            });
            
            const result = await wingmanPowerUp.applyEffect(mockPlayerShip);
            
            expect(result).toBe(false);
            expect(wingmanPowerUp.wingmanShip).toBeNull();
        });

        test('should handle wingman creation with invalid position', () => {
            mockPlayerShip.position = null;
            
            const result = wingmanPowerUp.applyEffect(mockPlayerShip);
            
            expect(result).toBe(false);
        });

        test('should handle wingman with no add method in gameObjectManager', () => {
            mockPlayerShip.weaponSystem.gameObjectManager = {
                add: null
            };
            
            const result = wingmanPowerUp.applyEffect(mockPlayerShip);
            
            expect(result).toBe(false);
        });

        test('should handle wingman destroy method throwing exception', () => {
            // First apply the power-up
            wingmanPowerUp.applyEffect(mockPlayerShip);
            
            // Mock wingman to throw exception on destroy
            wingmanPowerUp.wingmanShip.destroy = jest.fn().mockImplementation(() => {
                throw new Error('Destroy failed');
            });
            
            const result = wingmanPowerUp.removeEffect(mockPlayerShip);
            
            expect(result).toBe(true); // Should still return true even if destroy fails
            expect(wingmanPowerUp.wingmanShip).toBeNull();
        });

        test('should handle multiple apply calls without remove', async () => {
            const result1 = await wingmanPowerUp.apply(mockPlayerShip);
            const result2 = await wingmanPowerUp.apply(mockPlayerShip);
            
            expect(result1).toBe(true);
            expect(result2).toBe(false); // Should fail because already active
        });
    });

    describe('RealityWarpPowerUp additional tests', () => {
        let realityWarpPowerUp;

        beforeEach(() => {
            realityWarpPowerUp = new RealityWarpPowerUp('advanced');
        });

        test('should handle player ship with no realityWarpTier property', () => {
            // Remove the property entirely
            delete mockPlayerShip.realityWarpTier;
            
            const result = realityWarpPowerUp.apply(mockPlayerShip);
            
            expect(result).toBe(true);
            expect(mockPlayerShip.realityWarpTier).toBe('advanced');
        });

        test('should handle player ship with undefined realityWarpTier', () => {
            mockPlayerShip.realityWarpTier = undefined;
            
            const result = realityWarpPowerUp.apply(mockPlayerShip);
            
            expect(result).toBe(true);
            expect(mockPlayerShip.realityWarpTier).toBe('advanced');
        });

        test('should handle player ship with null realityWarpTier', () => {
            mockPlayerShip.realityWarpTier = null;
            
            const result = realityWarpPowerUp.apply(mockPlayerShip);
            
            expect(result).toBe(true);
            expect(mockPlayerShip.realityWarpTier).toBe('advanced');
        });

        test('should handle remove when player ship has no realityWarpTier property', () => {
            realityWarpPowerUp.isActive = true;
            
            // Remove the property entirely
            delete mockPlayerShip.realityWarpTier;
            
            const result = realityWarpPowerUp.remove(mockPlayerShip);
            
            expect(result).toBe(true);
            expect(realityWarpPowerUp.isActive).toBe(false);
        });

        test('should handle remove when player ship has different warp tier', () => {
            realityWarpPowerUp.isActive = true;
            mockPlayerShip.realityWarpTier = 'ultimate';
            
            const result = realityWarpPowerUp.remove(mockPlayerShip);
            
            expect(result).toBe(true);
            expect(realityWarpPowerUp.isActive).toBe(false);
            expect(mockPlayerShip.realityWarpTier).toBe('ultimate'); // Should remain unchanged
        });

        test('should getDisplayInfo include all tier information', () => {
            const basicInfo = new RealityWarpPowerUp('basic').getDisplayInfo();
            const advancedInfo = new RealityWarpPowerUp('advanced').getDisplayInfo();
            const ultimateInfo = new RealityWarpPowerUp('ultimate').getDisplayInfo();
            
            expect(basicInfo.tier).toBe('basic');
            expect(basicInfo.tierName).toBe('Basic');
            
            expect(advancedInfo.tier).toBe('advanced');
            expect(advancedInfo.tierName).toBe('Advanced');
            
            expect(ultimateInfo.tier).toBe('ultimate');
            expect(ultimateInfo.tierName).toBe('Ultimate');
        });
    });

    describe('PowerUpFactory additional tests', () => {
        test('should handle case-insensitive power-up types', () => {
            // This test assumes the factory might be enhanced to handle case-insensitive input
            // For now, it demonstrates the expected behavior
            
            expect(() => {
                PowerUpFactory.createPowerUp('extra_life');
            }).toThrow('Unknown power-up type: extra_life');
        });

        test('should handle null or undefined power-up types', () => {
            expect(() => {
                PowerUpFactory.createPowerUp(null);
            }).toThrow('Unknown power-up type: null');
            
            expect(() => {
                PowerUpFactory.createPowerUp(undefined);
            }).toThrow('Unknown power-up type: undefined');
        });

        test('should handle empty string power-up type', () => {
            expect(() => {
                PowerUpFactory.createPowerUp('');
            }).toThrow('Unknown power-up type: ');
        });

        test('should create all power-ups with unique instances', () => {
            const allPowerUps1 = PowerUpFactory.createAllPowerUps();
            const allPowerUps2 = PowerUpFactory.createAllPowerUps();
            
            expect(allPowerUps1).toHaveLength(6);
            expect(allPowerUps2).toHaveLength(6);
            
            // All instances should be different
            for (let i = 0; i < allPowerUps1.length; i++) {
                expect(allPowerUps1[i]).not.toBe(allPowerUps2[i]);
                expect(allPowerUps1[i].id).not.toBe(allPowerUps2[i].id);
            }
        });

        test('should get all power-up types in consistent order', () => {
            const types1 = PowerUpFactory.getAllPowerUpTypes();
            const types2 = PowerUpFactory.getAllPowerUpTypes();
            
            expect(types1).toEqual(types2);
            expect(types1).toEqual([
                'EXTRA_LIFE',
                'SPREAD_AMMO',
                'EXTRA_WINGMAN',
                'REALITY_WARP_BASIC',
                'REALITY_WARP_ADVANCED',
                'REALITY_WARP_ULTIMATE'
            ]);
        });
    });

    describe('PowerUp integration tests', () => {
        test('should handle apply and remove cycle correctly', async () => {
            const spreadAmmoPowerUp = new SpreadAmmoPowerUp();
            
            // Apply the power-up
            const applyResult = await spreadAmmoPowerUp.apply(mockPlayerShip);
            expect(applyResult).toBe(true);
            expect(spreadAmmoPowerUp.isActive).toBe(true);
            expect(mockPlayerShip.weaponSystem.enableSpreadPattern).toHaveBeenCalledWith(true);
            
            // Remove the power-up
            const removeResult = spreadAmmoPowerUp.remove(mockPlayerShip);
            expect(removeResult).toBe(true);
            expect(spreadAmmoPowerUp.isActive).toBe(false);
            expect(mockPlayerShip.weaponSystem.enableSpreadPattern).toHaveBeenCalledWith(false);
            
            // Should be able to apply again
            const secondApplyResult = await spreadAmmoPowerUp.apply(mockPlayerShip);
            expect(secondApplyResult).toBe(true);
        });

        test('should handle power-up expiration through update', async () => {
            const spreadAmmoPowerUp = new SpreadAmmoPowerUp();
            
            // Apply the power-up
            await spreadAmmoPowerUp.apply(mockPlayerShip);
            expect(spreadAmmoPowerUp.isActive).toBe(true);
            
            // Update with deltaTime greater than duration
            const result = spreadAmmoPowerUp.update(40000, mockPlayerShip);
            
            expect(result).toBe(false);
            expect(spreadAmmoPowerUp.isActive).toBe(false);
            expect(mockPlayerShip.weaponSystem.enableSpreadPattern).toHaveBeenCalledWith(false);
        });

        test('should handle multiple power-ups of different types', async () => {
            const extraLifePowerUp = new ExtraLifePowerUp();
            const spreadAmmoPowerUp = new SpreadAmmoPowerUp();
            
            // Apply both power-ups
            const lifeResult = await extraLifePowerUp.apply(mockPlayerShip);
            const spreadResult = await spreadAmmoPowerUp.apply(mockPlayerShip);
            
            expect(lifeResult).toBe(true);
            expect(spreadResult).toBe(true);
            expect(mockPlayerShip.lives).toBe(4); // 3 + 1
            expect(mockPlayerShip.weaponSystem.enableSpreadPattern).toHaveBeenCalledWith(true);
            
            // Remove only the spread ammo
            spreadAmmoPowerUp.remove(mockPlayerShip);
            
            expect(spreadAmmoPowerUp.isActive).toBe(false);
            expect(extraLifePowerUp.isActive).toBe(true); // Extra life should still be active
            expect(mockPlayerShip.weaponSystem.enableSpreadPattern).toHaveBeenCalledWith(false);
        });

        test('should handle power-up display info with active state', () => {
            const powerUp = new PowerUp('TEST', 100, 5000, 'Test');
            
            // Check display info when inactive
            const inactiveInfo = powerUp.getDisplayInfo();
            expect(inactiveInfo.isActive).toBe(false);
            expect(inactiveInfo.timeRemainingPercentage).toBe(0);
            expect(inactiveInfo.formattedTimeRemaining).toBe('Inactive');
            
            // Apply power-up
            powerUp.isActive = true;
            powerUp.timeRemaining = 2500;
            
            // Check display info when active
            const activeInfo = powerUp.getDisplayInfo();
            expect(activeInfo.isActive).toBe(true);
            expect(activeInfo.timeRemainingPercentage).toBe(0.5);
            expect(activeInfo.formattedTimeRemaining).toBe('3s');
        });

        test('should handle power-up display info with permanent duration', () => {
            const extraLifePowerUp = new ExtraLifePowerUp();
            
            // Permanent power-ups should show as permanent even when inactive
            const inactiveInfo = extraLifePowerUp.getDisplayInfo();
            expect(inactiveInfo.timeRemainingPercentage).toBe(1);
            expect(inactiveInfo.formattedTimeRemaining).toBe('Permanent');
            
            // Activate the power-up
            extraLifePowerUp.isActive = true;
            
            // Should still show as permanent
            const activeInfo = extraLifePowerUp.getDisplayInfo();
            expect(activeInfo.timeRemainingPercentage).toBe(1);
            expect(activeInfo.formattedTimeRemaining).toBe('Permanent');
        });
    });
});