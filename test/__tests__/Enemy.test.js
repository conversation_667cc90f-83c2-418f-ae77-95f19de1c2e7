import { Enemy } from '../../src/entities/Enemy.js';
import { Vector2 } from '../../src/utils/Vector2.js';
import { GAME_CONFIG, ENEMY_TYPES } from '../../src/config/gameConfig.js';

// Mock dependencies
jest.mock('../../src/utils/Vector2.js');
jest.mock('../../src/config/gameConfig.js');

describe('Enemy', () => {
  let enemy;
  let mockPosition;

  beforeEach(() => {
    // Reset mocks
    Vector2.mockClear();
    
    // Create mock position
    mockPosition = new Vector2(100, 100);
    
    // Create enemy instance
    enemy = new Enemy(100, 100, ENEMY_TYPES.AIR);
    
    // Clear console mocks
    jest.clearAllMocks();
  });

  describe('Constructor', () => {
    test('should initialize with default values', () => {
      expect(enemy.position).toBe(mockPosition);
      expect(enemy.type).toBe(ENEMY_TYPES.AIR);
      expect(enemy.active).toBe(true);
      expect(enemy.destroyed).toBe(false);
      expect(enemy.isDestroyed).toBe(false);
      expect(enemy.health).toBeGreaterThan(0);
      expect(enemy.maxHealth).toBeGreaterThan(0);
      expect(enemy.speed).toBeGreaterThan(0);
      expect(enemy.collisionRadius).toBeGreaterThan(0);
      expect(enemy.scoreValue).toBeGreaterThan(0);
      expect(enemy.color).toBeDefined();
      expect(enemy.size).toBeGreaterThan(0);
      expect(enemy.movementPattern).toBeDefined();
      expect(enemy.enemyId).toBeDefined();
      expect(enemy.animationFrame).toBe(0);
      expect(enemy.animationTimer).toBe(0);
      expect(enemy.animationSpeed).toBeGreaterThan(0);
      expect(enemy.lastShotTime).toBe(0);
      expect(enemy.fireRate).toBeGreaterThan(0);
      expect(enemy.canFire).toBe(true);
      expect(enemy.projectileSpeed).toBeGreaterThan(0);
      expect(enemy.projectileDamage).toBeGreaterThan(0);
      expect(enemy.stunned).toBe(false);
      expect(enemy.stunTimer).toBe(0);
      expect(enemy.slowed).toBe(false);
      expect(enemy.slowTimer).toBe(0);
      expect(enemy.slowMultiplier).toBeGreaterThan(0);
      expect(enemy.slowMultiplier).toBeLessThan(1);
      expect(enemy.burning).toBe(false);
      expect(enemy.burnTimer).toBe(0);
      expect(enemy.burnDamage).toBeGreaterThan(0);
      expect(enemy.burnInterval).toBeGreaterThan(0);
      expect(enemy.lastBurnDamageTime).toBe(0);
      expect(enemy.frozen).toBe(false);
      expect(enemy.freezeTimer).toBe(0);
      expect(enemy.shielded).toBe(false);
      expect(enemy.shieldHealth).toBe(0);
      expect(enemy.shieldMaxHealth).toBe(0);
      expect(enemy.enraged).toBe(false);
      expect(enemy.enrageTimer).toBe(0);
      expect(enemy.enrageMultiplier).toBeGreaterThan(1);
      expect(enemy.telegraphingAttack).toBe(false);
      expect(enemy.telegraphTimer).toBe(0);
      expect(enemy.telegraphDuration).toBeGreaterThan(0);
      expect(enemy.phase).toBe(1);
      expect(enemy.maxPhases).toBe(1);
      expect(enemy.phaseTransitionHealth).toBe(0);
      expect(enemy.abilities).toEqual([]);
      expect(enemy.abilityCooldowns).toEqual({});
      expect(enemy.environmentalEffects).toEqual([]);
      expect(enemy.customBehaviors).toEqual([]);
      expect(enemy.behaviorTimers).toEqual({});
      expect(enemy.targetPosition).toBeNull();
      expect(enemy.patrolPoints).toEqual([]);
      expect(enemy.currentPatrolIndex).toBe(0);
      expect(enemy.patrolWaitTime).toBe(0);
      expect(enemy.patrolWaitTimer).toBe(0);
      expect(enemy.formationPosition).toBeNull();
      expect(enemy.formationLeader).toBeNull();
      expect(enemy.formationOffset).toBeNull();
      expect(enemy.spawnTime).toBeGreaterThan(0);
      expect(enemy.timeAlive).toBe(0);
      expect(enemy.difficultyScaling).toBe(1.0);
      expect(enemy.damageTaken).toBe(0);
      expect(enemy.timesHit).toBe(0);
      expect(enemy.lastHitBy).toBeNull();
      expect(enemy.killedBy).toBeNull();
      expect(enemy.deathAnimationProgress).toBe(0);
      expect(enemy.deathAnimationDuration).toBeGreaterThan(0);
      expect(enemy.deathAnimationFrame).toBe(0);
      expect(enemy.spawnAnimationProgress).toBe(0);
      expect(enemy.spawnAnimationDuration).toBeGreaterThan(0);
      expect(enemy.spawnAnimationFrame).toBe(0);
      expect(enemy.isSpawning).toBe(true);
      expect(enemy.respawnable).toBe(false);
      expect(enemy.respawnTime).toBe(0);
      expect(enemy.respawnTimer).toBe(0);
      expect(enemy.drops).toEqual([]);
      expect(enemy.dropChance).toBeGreaterThanOrEqual(0);
      expect(enemy.dropChance).toBeLessThanOrEqual(1);
      expect(enemy.experienceValue).toBeGreaterThan(0);
      expect(enemy.specialEffects).toEqual([]);
      expect(enemy.soundEffects).toEqual({});
      expect(enemy.debugMode).toBe(false);
    });

    test('should set properties based on enemy type', () => {
      const airEnemy = new Enemy(100, 100, ENEMY_TYPES.AIR);
      const waterEnemy = new Enemy(100, 100, ENEMY_TYPES.WATER);
      const fireEnemy = new Enemy(100, 100, ENEMY_TYPES.FIRE);
      
      expect(airEnemy.color).not.toBe(waterEnemy.color);
      expect(waterEnemy.color).not.toBe(fireEnemy.color);
      expect(airEnemy.health).not.toBe(waterEnemy.health);
      expect(waterEnemy.health).not.toBe(fireEnemy.health);
    });

    test('should generate unique enemy ID', () => {
      const enemy1 = new Enemy(100, 100, ENEMY_TYPES.AIR);
      const enemy2 = new Enemy(100, 100, ENEMY_TYPES.AIR);
      
      expect(enemy1.enemyId).not.toBe(enemy2.enemyId);
    });

    test('should set spawn time', () => {
      const beforeSpawn = Date.now();
      const enemy = new Enemy(100, 100, ENEMY_TYPES.AIR);
      const afterSpawn = Date.now();
      
      expect(enemy.spawnTime).toBeGreaterThanOrEqual(beforeSpawn);
      expect(enemy.spawnTime).toBeLessThanOrEqual(afterSpawn);
    });
  });

  describe('update', () => {
    test('should update time alive', () => {
      const deltaTime = 100;
      enemy.timeAlive = 0;
      
      enemy.update(deltaTime);
      
      expect(enemy.timeAlive).toBe(deltaTime);
    });

    test('should update animation', () => {
      const deltaTime = 100;
      enemy.updateAnimation = jest.fn();
      
      enemy.update(deltaTime);
      
      expect(enemy.updateAnimation).toHaveBeenCalledWith(deltaTime);
    });

    test('should update movement', () => {
      const deltaTime = 100;
      const mockPlayerPosition = new Vector2(200, 200);
      enemy.updateMovement = jest.fn();
      
      enemy.update(deltaTime, mockPlayerPosition);
      
      expect(enemy.updateMovement).toHaveBeenCalledWith(deltaTime, mockPlayerPosition);
    });

    test('should update state effects', () => {
      const deltaTime = 100;
      enemy.updateStateEffects = jest.fn();
      
      enemy.update(deltaTime);
      
      expect(enemy.updateStateEffects).toHaveBeenCalledWith(deltaTime);
    });

    test('should update abilities', () => {
      const deltaTime = 100;
      enemy.updateAbilities = jest.fn();
      
      enemy.update(deltaTime);
      
      expect(enemy.updateAbilities).toHaveBeenCalledWith(deltaTime);
    });

    test('should update custom behaviors', () => {
      const deltaTime = 100;
      enemy.updateCustomBehaviors = jest.fn();
      
      enemy.update(deltaTime);
      
      expect(enemy.updateCustomBehaviors).toHaveBeenCalledWith(deltaTime);
    });

    test('should update firing', () => {
      const deltaTime = 100;
      const mockPlayerPosition = new Vector2(200, 200);
      enemy.updateFiring = jest.fn();
      
      enemy.update(deltaTime, mockPlayerPosition);
      
      expect(enemy.updateFiring).toHaveBeenCalledWith(deltaTime, mockPlayerPosition);
    });

    test('should check if enemy is out of bounds', () => {
      const deltaTime = 100;
      enemy.checkOutOfBounds = jest.fn();
      
      enemy.update(deltaTime);
      
      expect(enemy.checkOutOfBounds).toHaveBeenCalled();
    });

    test('should not update if enemy is destroyed', () => {
      const deltaTime = 100;
      enemy.destroyed = true;
      enemy.updateAnimation = jest.fn();
      enemy.updateMovement = jest.fn();
      
      enemy.update(deltaTime);
      
      expect(enemy.updateAnimation).not.toHaveBeenCalled();
      expect(enemy.updateMovement).not.toHaveBeenCalled();
    });
  });

  describe('updateAnimation', () => {
    test('should update animation timer', () => {
      const deltaTime = 100;
      enemy.animationTimer = 0;
      
      enemy.updateAnimation(deltaTime);
      
      expect(enemy.animationTimer).toBe(deltaTime);
    });

    test('should increment animation frame when timer exceeds speed', () => {
      const deltaTime = 100;
      enemy.animationTimer = enemy.animationSpeed - 10;
      enemy.animationFrame = 0;
      
      enemy.updateAnimation(deltaTime);
      
      expect(enemy.animationFrame).toBe(1);
      expect(enemy.animationTimer).toBeLessThan(enemy.animationSpeed);
    });

    test('should reset animation frame when it exceeds max', () => {
      const deltaTime = 100;
      enemy.animationTimer = enemy.animationSpeed - 10;
      enemy.animationFrame = 3; // Assuming max is 3
      
      enemy.updateAnimation(deltaTime);
      
      expect(enemy.animationFrame).toBe(0);
    });

    test('should update death animation if destroyed', () => {
      const deltaTime = 100;
      enemy.destroyed = true;
      enemy.deathAnimationProgress = 0;
      
      enemy.updateAnimation(deltaTime);
      
      expect(enemy.deathAnimationProgress).toBeGreaterThan(0);
    });

    test('should update spawn animation if spawning', () => {
      const deltaTime = 100;
      enemy.isSpawning = true;
      enemy.spawnAnimationProgress = 0;
      
      enemy.updateAnimation(deltaTime);
      
      expect(enemy.spawnAnimationProgress).toBeGreaterThan(0);
    });

    test('should complete spawn animation when progress reaches 1', () => {
      const deltaTime = 100;
      enemy.isSpawning = true;
      enemy.spawnAnimationProgress = 0.9;
      enemy.spawnAnimationDuration = 1000;
      
      enemy.updateAnimation(deltaTime);
      
      expect(enemy.isSpawning).toBe(false);
      expect(enemy.spawnAnimationProgress).toBe(1);
    });
  });

  describe('updateMovement', () => {
    test('should not move if stunned', () => {
      const deltaTime = 100;
      const mockPlayerPosition = new Vector2(200, 200);
      enemy.stunned = true;
      enemy.move = jest.fn();
      
      enemy.updateMovement(deltaTime, mockPlayerPosition);
      
      expect(enemy.move).not.toHaveBeenCalled();
    });

    test('should not move if frozen', () => {
      const deltaTime = 100;
      const mockPlayerPosition = new Vector2(200, 200);
      enemy.frozen = true;
      enemy.move = jest.fn();
      
      enemy.updateMovement(deltaTime, mockPlayerPosition);
      
      expect(enemy.move).not.toHaveBeenCalled();
    });

    test('should move based on movement pattern', () => {
      const deltaTime = 100;
      const mockPlayerPosition = new Vector2(200, 200);
      enemy.movementPattern = 'chase';
      enemy.moveChase = jest.fn();
      
      enemy.updateMovement(deltaTime, mockPlayerPosition);
      
      expect(enemy.moveChase).toHaveBeenCalledWith(deltaTime, mockPlayerPosition);
    });

    test('should move in formation if in formation', () => {
      const deltaTime = 100;
      const mockPlayerPosition = new Vector2(200, 200);
      enemy.formationLeader = {};
      enemy.formationOffset = { x: 10, y: 10 };
      enemy.moveInFormation = jest.fn();
      
      enemy.updateMovement(deltaTime, mockPlayerPosition);
      
      expect(enemy.moveInFormation).toHaveBeenCalledWith(deltaTime, mockPlayerPosition);
    });
  });

  describe('updateStateEffects', () => {
    test('should update stun timer', () => {
      const deltaTime = 100;
      enemy.stunned = true;
      enemy.stunTimer = 150;
      
      enemy.updateStateEffects(deltaTime);
      
      expect(enemy.stunTimer).toBe(50);
    });

    test('should remove stun when timer reaches 0', () => {
      const deltaTime = 100;
      enemy.stunned = true;
      enemy.stunTimer = 50;
      
      enemy.updateStateEffects(deltaTime);
      
      expect(enemy.stunned).toBe(false);
      expect(enemy.stunTimer).toBe(0);
    });

    test('should update slow timer', () => {
      const deltaTime = 100;
      enemy.slowed = true;
      enemy.slowTimer = 150;
      
      enemy.updateStateEffects(deltaTime);
      
      expect(enemy.slowTimer).toBe(50);
    });

    test('should remove slow when timer reaches 0', () => {
      const deltaTime = 100;
      enemy.slowed = true;
      enemy.slowTimer = 50;
      
      enemy.updateStateEffects(deltaTime);
      
      expect(enemy.slowed).toBe(false);
      expect(enemy.slowTimer).toBe(0);
    });

    test('should update burn timer and apply damage', () => {
      const deltaTime = 100;
      enemy.burning = true;
      enemy.burnTimer = 150;
      enemy.lastBurnDamageTime = 0;
      enemy.health = 100;
      enemy.takeDamage = jest.fn();
      
      enemy.updateStateEffects(deltaTime);
      
      expect(enemy.burnTimer).toBe(50);
      expect(enemy.takeDamage).toHaveBeenCalledWith(enemy.burnDamage, 'burn');
    });

    test('should remove burn when timer reaches 0', () => {
      const deltaTime = 100;
      enemy.burning = true;
      enemy.burnTimer = 50;
      
      enemy.updateStateEffects(deltaTime);
      
      expect(enemy.burning).toBe(false);
      expect(enemy.burnTimer).toBe(0);
    });

    test('should update freeze timer', () => {
      const deltaTime = 100;
      enemy.frozen = true;
      enemy.freezeTimer = 150;
      
      enemy.updateStateEffects(deltaTime);
      
      expect(enemy.freezeTimer).toBe(50);
    });

    test('should remove freeze when timer reaches 0', () => {
      const deltaTime = 100;
      enemy.frozen = true;
      enemy.freezeTimer = 50;
      
      enemy.updateStateEffects(deltaTime);
      
      expect(enemy.frozen).toBe(false);
      expect(enemy.freezeTimer).toBe(0);
    });

    test('should update enrage timer', () => {
      const deltaTime = 100;
      enemy.enraged = true;
      enemy.enrageTimer = 150;
      
      enemy.updateStateEffects(deltaTime);
      
      expect(enemy.enrageTimer).toBe(50);
    });

    test('should remove enrage when timer reaches 0', () => {
      const deltaTime = 100;
      enemy.enraged = true;
      enemy.enrageTimer = 50;
      
      enemy.updateStateEffects(deltaTime);
      
      expect(enemy.enraged).toBe(false);
      expect(enemy.enrageTimer).toBe(0);
    });

    test('should update telegraph timer', () => {
      const deltaTime = 100;
      enemy.telegraphingAttack = true;
      enemy.telegraphTimer = 150;
      
      enemy.updateStateEffects(deltaTime);
      
      expect(enemy.telegraphTimer).toBe(50);
    });

    test('should remove telegraph when timer reaches 0', () => {
      const deltaTime = 100;
      enemy.telegraphingAttack = true;
      enemy.telegraphTimer = 50;
      
      enemy.updateStateEffects(deltaTime);
      
      expect(enemy.telegraphingAttack).toBe(false);
      expect(enemy.telegraphTimer).toBe(0);
    });
  });

  describe('updateAbilities', () => {
    test('should update ability cooldowns', () => {
      const deltaTime = 100;
      enemy.abilityCooldowns = { 'ability1': 150, 'ability2': 50 };
      
      enemy.updateAbilities(deltaTime);
      
      expect(enemy.abilityCooldowns['ability1']).toBe(50);
      expect(enemy.abilityCooldowns['ability2']).toBe(0);
    });

    test('should remove cooldown when it reaches 0', () => {
      const deltaTime = 100;
      enemy.abilityCooldowns = { 'ability1': 50 };
      
      enemy.updateAbilities(deltaTime);
      
      expect(enemy.abilityCooldowns['ability1']).toBeUndefined();
    });
  });

  describe('updateCustomBehaviors', () => {
    test('should update behavior timers', () => {
      const deltaTime = 100;
      enemy.behaviorTimers = { 'behavior1': 150, 'behavior2': 50 };
      
      enemy.updateCustomBehaviors(deltaTime);
      
      expect(enemy.behaviorTimers['behavior1']).toBe(50);
      expect(enemy.behaviorTimers['behavior2']).toBe(0);
    });

    test('should remove behavior timer when it reaches 0', () => {
      const deltaTime = 100;
      enemy.behaviorTimers = { 'behavior1': 50 };
      
      enemy.updateCustomBehaviors(deltaTime);
      
      expect(enemy.behaviorTimers['behavior1']).toBeUndefined();
    });
  });

  describe('updateFiring', () => {
    test('should not fire if cannot fire', () => {
      const deltaTime = 100;
      const mockPlayerPosition = new Vector2(200, 200);
      enemy.canFire = false;
      enemy.fire = jest.fn();
      
      enemy.updateFiring(deltaTime, mockPlayerPosition);
      
      expect(enemy.fire).not.toHaveBeenCalled();
    });

    test('should not fire if stunned', () => {
      const deltaTime = 100;
      const mockPlayerPosition = new Vector2(200, 200);
      enemy.stunned = true;
      enemy.fire = jest.fn();
      
      enemy.updateFiring(deltaTime, mockPlayerPosition);
      
      expect(enemy.fire).not.toHaveBeenCalled();
    });

    test('should not fire if frozen', () => {
      const deltaTime = 100;
      const mockPlayerPosition = new Vector2(200, 200);
      enemy.frozen = true;
      enemy.fire = jest.fn();
      
      enemy.updateFiring(deltaTime, mockPlayerPosition);
      
      expect(enemy.fire).not.toHaveBeenCalled();
    });

    test('should not fire if no player position', () => {
      const deltaTime = 100;
      enemy.fire = jest.fn();
      
      enemy.updateFiring(deltaTime, null);
      
      expect(enemy.fire).not.toHaveBeenCalled();
    });

    test('should update fire cooldown', () => {
      const deltaTime = 100;
      const mockPlayerPosition = new Vector2(200, 200);
      enemy.lastShotTime = Date.now() - 1000;
      enemy.fireRate = 500;
      enemy.canFire = false;
      enemy.fire = jest.fn();
      
      enemy.updateFiring(deltaTime, mockPlayerPosition);
      
      expect(enemy.canFire).toBe(true);
    });

    test('should fire when conditions are met', () => {
      const deltaTime = 100;
      const mockPlayerPosition = new Vector2(200, 200);
      enemy.canFire = true;
      enemy.fire = jest.fn();
      
      enemy.updateFiring(deltaTime, mockPlayerPosition);
      
      expect(enemy.fire).toHaveBeenCalledWith(mockPlayerPosition);
    });
  });

  describe('move', () => {
    test('should move enemy based on velocity', () => {
      const deltaTime = 100;
      enemy.velocity = { x: 1, y: 1 };
      enemy.position = { x: 100, y: 100 };
      
      enemy.move(deltaTime);
      
      expect(enemy.position.x).toBe(101);
      expect(enemy.position.y).toBe(101);
    });

    test('should apply slow multiplier if slowed', () => {
      const deltaTime = 100;
      enemy.velocity = { x: 1, y: 1 };
      enemy.position = { x: 100, y: 100 };
      enemy.slowed = true;
      enemy.slowMultiplier = 0.5;
      
      enemy.move(deltaTime);
      
      expect(enemy.position.x).toBe(100.5);
      expect(enemy.position.y).toBe(100.5);
    });

    test('should apply enrage multiplier if enraged', () => {
      const deltaTime = 100;
      enemy.velocity = { x: 1, y: 1 };
      enemy.position = { x: 100, y: 100 };
      enemy.enraged = true;
      enemy.enrageMultiplier = 1.5;
      
      enemy.move(deltaTime);
      
      expect(enemy.position.x).toBe(101.5);
      expect(enemy.position.y).toBe(101.5);
    });
  });

  describe('moveChase', () => {
    test('should move towards player position', () => {
      const deltaTime = 100;
      const mockPlayerPosition = new Vector2(200, 200);
      enemy.position = { x: 100, y: 100 };
      enemy.speed = 1;
      
      enemy.moveChase(deltaTime, mockPlayerPosition);
      
      expect(enemy.velocity.x).toBeGreaterThan(0);
      expect(enemy.velocity.y).toBeGreaterThan(0);
    });

    test('should normalize velocity vector', () => {
      const deltaTime = 100;
      const mockPlayerPosition = new Vector2(200, 200);
      enemy.position = { x: 100, y: 100 };
      enemy.speed = 1;
      
      enemy.moveChase(deltaTime, mockPlayerPosition);
      
      const magnitude = Math.sqrt(enemy.velocity.x * enemy.velocity.x + enemy.velocity.y * enemy.velocity.y);
      expect(magnitude).toBeCloseTo(enemy.speed);
    });
  });

  describe('movePattern', () => {
    test('should move in sine wave pattern', () => {
      const deltaTime = 100;
      enemy.position = { x: 100, y: 100 };
      enemy.speed = 1;
      enemy.timeAlive = 0;
      
      enemy.movePattern(deltaTime, 'sine');
      
      expect(enemy.velocity.x).toBe(0);
      expect(enemy.velocity.y).toBe(1);
    });

    test('should move in circular pattern', () => {
      const deltaTime = 100;
      enemy.position = { x: 100, y: 100 };
      enemy.speed = 1;
      enemy.timeAlive = 0;
      
      enemy.movePattern(deltaTime, 'circular');
      
      expect(enemy.velocity.x).toBe(1);
      expect(enemy.velocity.y).toBe(0);
    });
  });

  describe('moveInFormation', () => {
    test('should move to formation position', () => {
      const deltaTime = 100;
      const mockPlayerPosition = new Vector2(200, 200);
      enemy.position = { x: 100, y: 100 };
      enemy.formationLeader = { position: { x: 150, y: 150 } };
      enemy.formationOffset = { x: 10, y: 10 };
      
      enemy.moveInFormation(deltaTime, mockPlayerPosition);
      
      expect(enemy.velocity.x).toBeGreaterThan(0);
      expect(enemy.velocity.y).toBeGreaterThan(0);
    });
  });

  describe('fire', () => {
    test('should create projectile', () => {
      const mockPlayerPosition = new Vector2(200, 200);
      enemy.position = { x: 100, y: 100 };
      enemy.createProjectile = jest.fn();
      
      enemy.fire(mockPlayerPosition);
      
      expect(enemy.createProjectile).toHaveBeenCalledWith(mockPlayerPosition);
      expect(enemy.lastShotTime).toBeGreaterThan(0);
      expect(enemy.canFire).toBe(false);
    });
  });

  describe('takeDamage', () => {
    test('should reduce health', () => {
      const damage = 10;
      enemy.health = 100;
      enemy.maxHealth = 100;
      
      enemy.takeDamage(damage);
      
      expect(enemy.health).toBe(90);
      expect(enemy.damageTaken).toBe(damage);
      expect(enemy.timesHit).toBe(1);
    });

    test('should damage shield if shielded', () => {
      const damage = 10;
      enemy.health = 100;
      enemy.maxHealth = 100;
      enemy.shielded = true;
      enemy.shieldHealth = 20;
      enemy.shieldMaxHealth = 20;
      
      enemy.takeDamage(damage);
      
      expect(enemy.health).toBe(100);
      expect(enemy.shieldHealth).toBe(10);
    });

    test('should remove shield when shield health reaches 0', () => {
      const damage = 10;
      enemy.health = 100;
      enemy.maxHealth = 100;
      enemy.shielded = true;
      enemy.shieldHealth = 5;
      enemy.shieldMaxHealth = 20;
      
      enemy.takeDamage(damage);
      
      expect(enemy.shielded).toBe(false);
      expect(enemy.shieldHealth).toBe(0);
    });

    test('should destroy enemy when health reaches 0', () => {
      const damage = 100;
      enemy.health = 50;
      enemy.maxHealth = 100;
      enemy.destroy = jest.fn();
      
      enemy.takeDamage(damage);
      
      expect(enemy.health).toBe(0);
      expect(enemy.destroy).toHaveBeenCalled();
    });

    test('should check phase transition', () => {
      const damage = 10;
      enemy.health = 60;
      enemy.maxHealth = 100;
      enemy.phase = 1;
      enemy.maxPhases = 2;
      enemy.phaseTransitionHealth = 50;
      enemy.transitionToNextPhase = jest.fn();
      
      enemy.takeDamage(damage);
      
      expect(enemy.transitionToNextPhase).toHaveBeenCalled();
    });
  });

  describe('destroy', () => {
    test('should set destroyed flags', () => {
      enemy.destroy();
      
      expect(enemy.destroyed).toBe(true);
      expect(enemy.isDestroyed).toBe(true);
      expect(enemy.active).toBe(false);
    });

    test('should reset death animation progress', () => {
      enemy.deathAnimationProgress = 0.5;
      
      enemy.destroy();
      
      expect(enemy.deathAnimationProgress).toBe(0);
    });
  });

  describe('reset', () => {
    beforeEach(() => {
      // Set up some state
      enemy.health = 50;
      enemy.destroyed = true;
      enemy.stunned = true;
      enemy.slowed = true;
      enemy.burning = true;
      enemy.frozen = true;
      enemy.enraged = true;
      enemy.telegraphingAttack = true;
      enemy.shielded = true;
    });

    test('should reset all properties to initial state', () => {
      enemy.reset(100, 200, ENEMY_TYPES.WATER);
      
      expect(enemy.position.x).toBe(100);
      expect(enemy.position.y).toBe(200);
      expect(enemy.type).toBe(ENEMY_TYPES.WATER);
      expect(enemy.active).toBe(true);
      expect(enemy.destroyed).toBe(false);
      expect(enemy.isDestroyed).toBe(false);
      expect(enemy.health).toBeGreaterThan(0);
      expect(enemy.stunned).toBe(false);
      expect(enemy.slowed).toBe(false);
      expect(enemy.burning).toBe(false);
      expect(enemy.frozen).toBe(false);
      expect(enemy.enraged).toBe(false);
      expect(enemy.telegraphingAttack).toBe(false);
      expect(enemy.shielded).toBe(false);
    });

    test('should reset timers and counters', () => {
      enemy.reset(100, 200, ENEMY_TYPES.WATER);
      
      expect(enemy.animationFrame).toBe(0);
      expect(enemy.animationTimer).toBe(0);
      expect(enemy.lastShotTime).toBe(0);
      expect(enemy.stunTimer).toBe(0);
      expect(enemy.slowTimer).toBe(0);
      expect(enemy.burnTimer).toBe(0);
      expect(enemy.freezeTimer).toBe(0);
      expect(enemy.enrageTimer).toBe(0);
      expect(enemy.telegraphTimer).toBe(0);
      expect(enemy.damageTaken).toBe(0);
      expect(enemy.timesHit).toBe(0);
      expect(enemy.timeAlive).toBe(0);
    });
  });

  describe('applyStatusEffect', () => {
    test('should apply stun effect', () => {
      enemy.applyStatusEffect('stun', 1000);
      
      expect(enemy.stunned).toBe(true);
      expect(enemy.stunTimer).toBe(1000);
    });

    test('should apply slow effect', () => {
      enemy.applyStatusEffect('slow', 1000, 0.5);
      
      expect(enemy.slowed).toBe(true);
      expect(enemy.slowTimer).toBe(1000);
      expect(enemy.slowMultiplier).toBe(0.5);
    });

    test('should apply burn effect', () => {
      enemy.applyStatusEffect('burn', 1000, 5);
      
      expect(enemy.burning).toBe(true);
      expect(enemy.burnTimer).toBe(1000);
      expect(enemy.burnDamage).toBe(5);
    });

    test('should apply freeze effect', () => {
      enemy.applyStatusEffect('freeze', 1000);
      
      expect(enemy.frozen).toBe(true);
      expect(enemy.freezeTimer).toBe(1000);
    });

    test('should apply enrage effect', () => {
      enemy.applyStatusEffect('enrage', 1000, 1.5);
      
      expect(enemy.enraged).toBe(true);
      expect(enemy.enrageTimer).toBe(1000);
      expect(enemy.enrageMultiplier).toBe(1.5);
    });

    test('should apply shield effect', () => {
      enemy.applyStatusEffect('shield', 0, 50);
      
      expect(enemy.shielded).toBe(true);
      expect(enemy.shieldHealth).toBe(50);
      expect(enemy.shieldMaxHealth).toBe(50);
    });
  });

  describe('checkOutOfBounds', () => {
    test('should destroy enemy if out of bounds', () => {
      enemy.position = { x: -100, y: 100 };
      enemy.destroy = jest.fn();
      
      enemy.checkOutOfBounds();
      
      expect(enemy.destroy).toHaveBeenCalled();
    });

    test('should not destroy enemy if in bounds', () => {
      enemy.position = { x: 100, y: 100 };
      enemy.destroy = jest.fn();
      
      enemy.checkOutOfBounds();
      
      expect(enemy.destroy).not.toHaveBeenCalled();
    });
  });

  describe('transitionToNextPhase', () => {
    test('should increment phase', () => {
      enemy.phase = 1;
      enemy.maxPhases = 3;
      
      enemy.transitionToNextPhase();
      
      expect(enemy.phase).toBe(2);
    });

    test('should not increment phase if at max', () => {
      enemy.phase = 3;
      enemy.maxPhases = 3;
      
      enemy.transitionToNextPhase();
      
      expect(enemy.phase).toBe(3);
    });

    test('should update phase transition health', () => {
      enemy.phase = 1;
      enemy.maxPhases = 3;
      enemy.maxHealth = 100;
      
      enemy.transitionToNextPhase();
      
      expect(enemy.phaseTransitionHealth).toBeLessThan(enemy.maxHealth);
    });
  });

  describe('useAbility', () => {
    test('should use ability if not on cooldown', () => {
      const mockAbility = jest.fn();
      enemy.abilities = { 'ability1': mockAbility };
      
      const result = enemy.useAbility('ability1');
      
      expect(mockAbility).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    test('should not use ability if on cooldown', () => {
      const mockAbility = jest.fn();
      enemy.abilities = { 'ability1': mockAbility };
      enemy.abilityCooldowns = { 'ability1': 1000 };
      
      const result = enemy.useAbility('ability1');
      
      expect(mockAbility).not.toHaveBeenCalled();
      expect(result).toBe(false);
    });

    test('should not use ability if ability does not exist', () => {
      const result = enemy.useAbility('nonexistentAbility');
      
      expect(result).toBe(false);
    });
  });

  describe('setCustomBehavior', () => {
    test('should set custom behavior', () => {
      const mockBehavior = jest.fn();
      
      enemy.setCustomBehavior('testBehavior', mockBehavior, 1000);
      
      expect(enemy.customBehaviors['testBehavior']).toBe(mockBehavior);
      expect(enemy.behaviorTimers['testBehavior']).toBe(1000);
    });
  });

  describe('setFormationPosition', () => {
    test('should set formation position', () => {
      const mockLeader = {};
      const mockOffset = { x: 10, y: 10 };
      
      enemy.setFormationPosition(mockLeader, mockOffset);
      
      expect(enemy.formationLeader).toBe(mockLeader);
      expect(enemy.formationOffset).toBe(mockOffset);
    });
  });

  describe('setPatrolPoints', () => {
    test('should set patrol points', () => {
      const mockPoints = [
        { x: 100, y: 100 },
        { x: 200, y: 200 },
        { x: 300, y: 300 }
      ];
      
      enemy.setPatrolPoints(mockPoints);
      
      expect(enemy.patrolPoints).toEqual(mockPoints);
      expect(enemy.currentPatrolIndex).toBe(0);
    });
  });

  describe('setTargetPosition', () => {
    test('should set target position', () => {
      const mockPosition = { x: 100, y: 100 };
      
      enemy.setTargetPosition(mockPosition);
      
      expect(enemy.targetPosition).toBe(mockPosition);
    });
  });

  describe('getEffectiveSpeed', () => {
    test('should return normal speed if no effects', () => {
      enemy.speed = 1;
      
      const result = enemy.getEffectiveSpeed();
      
      expect(result).toBe(1);
    });

    test('should apply slow multiplier if slowed', () => {
      enemy.speed = 1;
      enemy.slowed = true;
      enemy.slowMultiplier = 0.5;
      
      const result = enemy.getEffectiveSpeed();
      
      expect(result).toBe(0.5);
    });

    test('should apply enrage multiplier if enraged', () => {
      enemy.speed = 1;
      enemy.enraged = true;
      enemy.enrageMultiplier = 1.5;
      
      const result = enemy.getEffectiveSpeed();
      
      expect(result).toBe(1.5);
    });

    test('should apply both slow and enrage multipliers', () => {
      enemy.speed = 1;
      enemy.slowed = true;
      enemy.slowMultiplier = 0.5;
      enemy.enraged = true;
      enemy.enrageMultiplier = 1.5;
      
      const result = enemy.getEffectiveSpeed();
      
      expect(result).toBe(0.75); // 1 * 0.5 * 1.5
    });
  });

  describe('getEffectiveDamage', () => {
    test('should return normal damage if no effects', () => {
      enemy.projectileDamage = 10;
      
      const result = enemy.getEffectiveDamage();
      
      expect(result).toBe(10);
    });

    test('should apply enrage multiplier if enraged', () => {
      enemy.projectileDamage = 10;
      enemy.enraged = true;
      enemy.enrageMultiplier = 1.5;
      
      const result = enemy.getEffectiveDamage();
      
      expect(result).toBe(15);
    });
  });

  describe('getHealthPercentage', () => {
    test('should return correct health percentage', () => {
      enemy.health = 50;
      enemy.maxHealth = 100;
      
      const result = enemy.getHealthPercentage();
      
      expect(result).toBe(0.5);
    });

    test('should return 0 if health is 0', () => {
      enemy.health = 0;
      enemy.maxHealth = 100;
      
      const result = enemy.getHealthPercentage();
      
      expect(result).toBe(0);
    });

    test('should return 1 if health equals max health', () => {
      enemy.health = 100;
      enemy.maxHealth = 100;
      
      const result = enemy.getHealthPercentage();
      
      expect(result).toBe(1);
    });
  });

  describe('getShieldPercentage', () => {
    test('should return correct shield percentage', () => {
      enemy.shielded = true;
      enemy.shieldHealth = 25;
      enemy.shieldMaxHealth = 50;
      
      const result = enemy.getShieldPercentage();
      
      expect(result).toBe(0.5);
    });

    test('should return 0 if not shielded', () => {
      enemy.shielded = false;
      
      const result = enemy.getShieldPercentage();
      
      expect(result).toBe(0);
    });
  });

  describe('isAlive', () => {
    test('should return true if not destroyed', () => {
      enemy.destroyed = false;
      
      const result = enemy.isAlive();
      
      expect(result).toBe(true);
    });

    test('should return false if destroyed', () => {
      enemy.destroyed = true;
      
      const result = enemy.isAlive();
      
      expect(result).toBe(false);
    });
  });

  describe('canUseAbility', () => {
    test('should return true if ability exists and not on cooldown', () => {
      enemy.abilities = { 'ability1': jest.fn() };
      
      const result = enemy.canUseAbility('ability1');
      
      expect(result).toBe(true);
    });

    test('should return false if ability does not exist', () => {
      const result = enemy.canUseAbility('nonexistentAbility');
      
      expect(result).toBe(false);
    });

    test('should return false if ability is on cooldown', () => {
      enemy.abilities = { 'ability1': jest.fn() };
      enemy.abilityCooldowns = { 'ability1': 1000 };
      
      const result = enemy.canUseAbility('ability1');
      
      expect(result).toBe(false);
    });
  });

  describe('isInFormation', () => {
    test('should return true if in formation', () => {
      enemy.formationLeader = {};
      
      const result = enemy.isInFormation();
      
      expect(result).toBe(true);
    });

    test('should return false if not in formation', () => {
      enemy.formationLeader = null;
      
      const result = enemy.isInFormation();
      
      expect(result).toBe(false);
    });
  });

  describe('hasStatusEffect', () => {
    test('should return true if has status effect', () => {
      enemy.stunned = true;
      
      const result = enemy.hasStatusEffect('stun');
      
      expect(result).toBe(true);
    });

    test('should return false if does not have status effect', () => {
      enemy.stunned = false;
      
      const result = enemy.hasStatusEffect('stun');
      
      expect(result).toBe(false);
    });
  });
});